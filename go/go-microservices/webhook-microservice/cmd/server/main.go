package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"

	"webhook_microservice/internal/api"
	"webhook_microservice/internal/container"
)

func main() {
	// Create context that will be canceled on shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Create container with new pattern
	container := container.NewContainer()
	defer container.Close(ctx)

	// Initialize router
	router := gin.New()
	api.RegisterRoutes(router, container)

	// Add observability endpoints (always enabled for webhook microservice)
	router.GET("/metrics", gin.WrapH(promhttp.Handler()))

	// Start server
	addr := fmt.Sprintf(":%d", container.Config().Port)
	container.Logger().Info("Starting server", "address", addr)

	// Handle graceful shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		if err := router.Run(addr); err != nil {
			container.Logger().Error("Failed to start server", "error", err)
			cancel()
		}
	}()

	<-quit
	container.Logger().Info("Shutting down server...")
	
	// Give outstanding requests 30 seconds to complete
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()
	
	// Perform graceful shutdown
	if err := container.Close(shutdownCtx); err != nil {
		container.Logger().Error("Error during shutdown", "error", err)
	}
} 