
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<title>server: Go Coverage Report</title>
		<style>
			body {
				background: black;
				color: rgb(80, 80, 80);
			}
			body, pre, #legend span {
				font-family: Menlo, monospace;
				font-weight: bold;
			}
			#topbar {
				background: black;
				position: fixed;
				top: 0; left: 0; right: 0;
				height: 42px;
				border-bottom: 1px solid rgb(80, 80, 80);
			}
			#content {
				margin-top: 50px;
			}
			#nav, #legend {
				float: left;
				margin-left: 10px;
			}
			#legend {
				margin-top: 12px;
			}
			#nav {
				margin-top: 10px;
			}
			#legend span {
				margin: 0 5px;
			}
			.cov0 { color: rgb(192, 0, 0) }
.cov1 { color: rgb(128, 128, 128) }
.cov2 { color: rgb(116, 140, 131) }
.cov3 { color: rgb(104, 152, 134) }
.cov4 { color: rgb(92, 164, 137) }
.cov5 { color: rgb(80, 176, 140) }
.cov6 { color: rgb(68, 188, 143) }
.cov7 { color: rgb(56, 200, 146) }
.cov8 { color: rgb(44, 212, 149) }
.cov9 { color: rgb(32, 224, 152) }
.cov10 { color: rgb(20, 236, 155) }

		</style>
	</head>
	<body>
		<div id="topbar">
			<div id="nav">
				<select id="files">
				
				<option value="file0">webhook_microservice/cmd/server/main.go (0.0%)</option>
				
				<option value="file1">webhook_microservice/internal/api/middleware/validator.go (0.0%)</option>
				
				<option value="file2">webhook_microservice/internal/api/middleware/waf.go (0.0%)</option>
				
				<option value="file3">webhook_microservice/internal/api/response.go (62.5%)</option>
				
				<option value="file4">webhook_microservice/internal/api/router.go (27.3%)</option>
				
				<option value="file5">webhook_microservice/internal/api/webhook_handler.go (33.3%)</option>
				
				<option value="file6">webhook_microservice/internal/app/commands/handle_webhook.go (4.2%)</option>
				
				<option value="file7">webhook_microservice/internal/app/errors.go (0.0%)</option>
				
				<option value="file8">webhook_microservice/internal/config/config.go (87.9%)</option>
				
				<option value="file9">webhook_microservice/internal/container/container.go (54.2%)</option>
				
				<option value="file10">webhook_microservice/internal/domain/webhook.go (0.0%)</option>
				
				<option value="file11">webhook_microservice/internal/infra/dapr/pubsub.go (0.0%)</option>
				
				<option value="file12">webhook_microservice/internal/infra/dapr/state_client.go (2.7%)</option>
				
				<option value="file13">webhook_microservice/internal/infra/tracing/tracing.go (0.0%)</option>
				
				</select>
			</div>
			<div id="legend">
				<span>not tracked</span>
			
				<span class="cov0">not covered</span>
				<span class="cov8">covered</span>
			
			</div>
		</div>
		<div id="content">
		
		<pre class="file" id="file0" style="display: none">package main

import (
        "context"
        "fmt"
        "os"
        "os/signal"
        "syscall"
        "time"

        "github.com/gin-gonic/gin"
        "github.com/prometheus/client_golang/prometheus/promhttp"

        "webhook_microservice/internal/api"
        "webhook_microservice/internal/container"
)

func main() <span class="cov0" title="0">{
        // Create context that will be canceled on shutdown
        ctx, cancel := context.WithCancel(context.Background())
        defer cancel()

        // Create container with new pattern
        container := container.NewContainer()
        defer container.Close(ctx)

        // Initialize router
        router := gin.New()
        api.RegisterRoutes(router, container)

        // Add observability endpoints (always enabled for webhook microservice)
        router.GET("/metrics", gin.WrapH(promhttp.Handler()))

        // Start server
        addr := fmt.Sprintf(":%d", container.Config().Port)
        container.Logger().Info("Starting server", "address", addr)

        // Handle graceful shutdown
        quit := make(chan os.Signal, 1)
        signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

        go func() </span><span class="cov0" title="0">{
                if err := router.Run(addr); err != nil </span><span class="cov0" title="0">{
                        container.Logger().Error("Failed to start server", "error", err)
                        cancel()
                }</span>
        }()

        <span class="cov0" title="0">&lt;-quit
        container.Logger().Info("Shutting down server...")
        
        // Give outstanding requests 30 seconds to complete
        shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
        defer shutdownCancel()
        
        // Perform graceful shutdown
        if err := container.Close(shutdownCtx); err != nil </span><span class="cov0" title="0">{
                container.Logger().Error("Error during shutdown", "error", err)
        }</span>
} </pre>
		
		<pre class="file" id="file1" style="display: none">package middleware

import (
        "net/http"
        "strings"

        "github.com/gin-gonic/gin"
)

// ValidatorConfig holds validation configuration
type ValidatorConfig struct {
        RequiredHeaders []string
}

// ValidatorMiddleware validates required headers and basic webhook structure
func ValidatorMiddleware(config ValidatorConfig) gin.HandlerFunc <span class="cov0" title="0">{
        return func(c *gin.Context) </span><span class="cov0" title="0">{
                // Check required headers
                for _, header := range config.RequiredHeaders </span><span class="cov0" title="0">{
                        value := c.GetHeader(header)
                        if value == "" </span><span class="cov0" title="0">{
                                // For delivery ID, try alternative header names
                                if strings.Contains(strings.ToLower(header), "delivery") </span><span class="cov0" title="0">{
                                        if altValue := c.GetHeader("X-GitHub-Delivery"); altValue != "" </span><span class="cov0" title="0">{
                                                c.Set("delivery_id", altValue)
                                                continue</span>
                                        }
                                        <span class="cov0" title="0">if altValue := c.GetHeader("X-Hook-ID"); altValue != "" </span><span class="cov0" title="0">{
                                                c.Set("delivery_id", altValue)
                                                continue</span>
                                        }
                                }
                                
                                <span class="cov0" title="0">c.JSON(http.StatusBadRequest, gin.H{
                                        "error": "Missing required header: " + header,
                                        "code":  "MISSING_HEADER",
                                })
                                c.Abort()
                                return</span>
                        }
                        
                        // Store delivery ID for later use
                        <span class="cov0" title="0">if strings.Contains(strings.ToLower(header), "delivery") </span><span class="cov0" title="0">{
                                c.Set("delivery_id", value)
                        }</span>
                }
                
                // Validate Content-Type for POST requests
                <span class="cov0" title="0">if c.Request.Method == "POST" </span><span class="cov0" title="0">{
                        contentType := c.GetHeader("Content-Type")
                        if contentType == "" </span><span class="cov0" title="0">{
                                c.JSON(http.StatusBadRequest, gin.H{
                                        "error": "Content-Type header is required",
                                        "code":  "MISSING_CONTENT_TYPE",
                                })
                                c.Abort()
                                return
                        }</span>
                        
                        // Accept common webhook content types
                        <span class="cov0" title="0">validContentTypes := []string{
                                "application/json",
                                "application/x-www-form-urlencoded",
                                "text/plain",
                        }
                        
                        isValidContentType := false
                        for _, validType := range validContentTypes </span><span class="cov0" title="0">{
                                if strings.Contains(strings.ToLower(contentType), validType) </span><span class="cov0" title="0">{
                                        isValidContentType = true
                                        break</span>
                                }
                        }
                        
                        <span class="cov0" title="0">if !isValidContentType </span><span class="cov0" title="0">{
                                c.JSON(http.StatusUnsupportedMediaType, gin.H{
                                        "error": "Unsupported Content-Type: " + contentType,
                                        "code":  "UNSUPPORTED_CONTENT_TYPE",
                                })
                                c.Abort()
                                return
                        }</span>
                }
                
                <span class="cov0" title="0">c.Next()</span>
        }
}

// SignatureValidatorMiddleware validates webhook signatures (placeholder for future implementation)
func SignatureValidatorMiddleware() gin.HandlerFunc <span class="cov0" title="0">{
        return func(c *gin.Context) </span><span class="cov0" title="0">{
                // TODO: Implement signature validation based on webhook source
                // For now, just log the signature headers
                signature := c.GetHeader("X-Signature")
                if signature == "" </span><span class="cov0" title="0">{
                        signature = c.GetHeader("X-Hub-Signature")
                }</span>
                <span class="cov0" title="0">if signature == "" </span><span class="cov0" title="0">{
                        signature = c.GetHeader("X-Hub-Signature-256")
                }</span>
                
                // Store signature for potential validation in handler
                <span class="cov0" title="0">if signature != "" </span><span class="cov0" title="0">{
                        c.Set("webhook_signature", signature)
                }</span>
                
                <span class="cov0" title="0">c.Next()</span>
        }
}
</pre>
		
		<pre class="file" id="file2" style="display: none">package middleware

import (
        "net"
        "net/http"
        "strings"

        "github.com/gin-gonic/gin"
        "golang.org/x/time/rate"
)

// WAFConfig holds WAF configuration
type WAFConfig struct {
        AllowList    []string
        RateLimitRPM int
}

// WAFMiddleware implements IP allow-list and rate limiting
func WAFMiddleware(config WAFConfig) gin.HandlerFunc <span class="cov0" title="0">{
        // Create rate limiter (requests per minute converted to per second)
        limiter := rate.NewLimiter(rate.Limit(config.RateLimitRPM)/60, config.RateLimitRPM)
        
        return func(c *gin.Context) </span><span class="cov0" title="0">{
                clientIP := c.ClientIP()
                
                // Check IP allow-list
                if !isIPAllowed(clientIP, config.AllowList) </span><span class="cov0" title="0">{
                        c.JSON(http.StatusForbidden, gin.H{
                                "error": "IP not allowed",
                                "code":  "IP_FORBIDDEN",
                        })
                        c.Abort()
                        return
                }</span>
                
                // Check rate limit
                <span class="cov0" title="0">if !limiter.Allow() </span><span class="cov0" title="0">{
                        c.JSON(http.StatusTooManyRequests, gin.H{
                                "error": "Rate limit exceeded",
                                "code":  "RATE_LIMIT_EXCEEDED",
                        })
                        c.Abort()
                        return
                }</span>
                
                <span class="cov0" title="0">c.Next()</span>
        }
}

// isIPAllowed checks if an IP is in the allow list
func isIPAllowed(clientIP string, allowList []string) bool <span class="cov0" title="0">{
        if len(allowList) == 0 </span><span class="cov0" title="0">{
                return true // No restrictions if allow list is empty
        }</span>
        
        <span class="cov0" title="0">for _, allowed := range allowList </span><span class="cov0" title="0">{
                // Handle CIDR notation
                if strings.Contains(allowed, "/") </span><span class="cov0" title="0">{
                        _, network, err := net.ParseCIDR(allowed)
                        if err != nil </span><span class="cov0" title="0">{
                                continue</span>
                        }
                        <span class="cov0" title="0">ip := net.ParseIP(clientIP)
                        if ip != nil &amp;&amp; network.Contains(ip) </span><span class="cov0" title="0">{
                                return true
                        }</span>
                } else<span class="cov0" title="0"> {
                        // Handle exact IP match
                        if clientIP == allowed </span><span class="cov0" title="0">{
                                return true
                        }</span>
                }
        }
        
        <span class="cov0" title="0">return false</span>
}
</pre>
		
		<pre class="file" id="file3" style="display: none">package api

import (
        "net/http"

        "github.com/gin-gonic/gin"
)

// Response represents a standard API response
type Response struct {
        Success bool        `json:"success"`
        Data    interface{} `json:"data,omitempty"`
        Error   *Error      `json:"error,omitempty"`
}

// Error represents an API error
type Error struct {
        Code    string `json:"code"`
        Message string `json:"message"`
}

// NewSuccessResponse creates a new success response
func NewSuccessResponse(data interface{}) *Response <span class="cov8" title="1">{
        return &amp;Response{
                Success: true,
                Data:    data,
        }
}</span>

// NewErrorResponse creates a new error response
func NewErrorResponse(code string, message string) *Response <span class="cov8" title="1">{
        return &amp;Response{
                Success: false,
                Error: &amp;Error{
                        Code:    code,
                        Message: message,
                },
        }
}</span>

// JSON sends a JSON response
func JSON(c *gin.Context, status int, response *Response) <span class="cov8" title="1">{
        c.JSON(status, response)
}</span>

// Success sends a success response
func Success(c *gin.Context, data interface{}) <span class="cov8" title="1">{
        JSON(c, http.StatusOK, NewSuccessResponse(data))
}</span>

// Created sends a created response
func Created(c *gin.Context, data interface{}) <span class="cov0" title="0">{
        JSON(c, http.StatusCreated, NewSuccessResponse(data))
}</span>

// BadRequest sends a bad request response
func BadRequest(c *gin.Context, message string) <span class="cov8" title="1">{
        JSON(c, http.StatusBadRequest, NewErrorResponse("BAD_REQUEST", message))
}</span>

// NotFound sends a not found response
func NotFound(c *gin.Context, message string) <span class="cov0" title="0">{
        JSON(c, http.StatusNotFound, NewErrorResponse("NOT_FOUND", message))
}</span>

// InternalError sends an internal server error response
func InternalError(c *gin.Context, message string) <span class="cov0" title="0">{
        JSON(c, http.StatusInternalServerError, NewErrorResponse("INTERNAL_ERROR", message))
}</span> </pre>
		
		<pre class="file" id="file4" style="display: none">package api

import (
        "net/http"
        "time"

        "github.com/gin-gonic/gin"
        "github.com/Matrics-io/the-manhattan-project-huly/go/shared/tracing"
        "github.com/Matrics-io/the-manhattan-project-huly/go/shared/metrics"
        "webhook_microservice/internal/container"
)

// RegisterRoutes registers all API routes
func RegisterRoutes(router *gin.Engine, container *container.Container) <span class="cov0" title="0">{
        // Initialize shared metrics
        metrics.Initialize()

        // Add shared middleware
        router.Use(gin.Recovery())
        router.Use(gin.Logger())
        router.Use(tracing.Middleware("webhook-microservice"))
        router.Use(metrics.PrometheusMiddleware())

        // Add request logging middleware
        router.Use(func(c *gin.Context) </span><span class="cov0" title="0">{
                start := time.Now()
                path := c.Request.URL.Path
                method := c.Request.Method

                // Process request
                c.Next()

                // Log request details
                latency := time.Since(start)
                status := c.Writer.Status()
                clientIP := c.ClientIP()

                container.Logger().Info("Request processed",
                        "method", method,
                        "path", path,
                        "status", status,
                        "latency", latency,
                        "client_ip", clientIP,
                )
        }</span>)

        // Register health endpoints with container health check
        <span class="cov0" title="0">router.GET("/healthz", func(c *gin.Context) </span><span class="cov0" title="0">{
                Success(c, gin.H{"status": "ok"})
        }</span>)

        <span class="cov0" title="0">router.GET("/readyz", func(c *gin.Context) </span><span class="cov0" title="0">{
                if err := container.HealthCheck(c.Request.Context()); err != nil </span><span class="cov0" title="0">{
                        JSON(c, http.StatusServiceUnavailable, NewErrorResponse("SERVICE_UNAVAILABLE", "Service not ready"))
                        return
                }</span>
                <span class="cov0" title="0">Success(c, gin.H{"status": "ready"})</span>
        })

        // Register metrics endpoint
        <span class="cov0" title="0">metrics.RegisterMetricsEndpoint(router)

        // Register webhook routes
        webhookHandler := NewWebhookHandler(container)
        webhookHandler.RegisterRoutes(router)</span>
}

// RegisterTestRoutes registers routes for testing without middleware
func RegisterTestRoutes(router *gin.Engine, container *container.Container) <span class="cov8" title="1">{
        // Initialize shared metrics
        metrics.Initialize()

        // Add minimal middleware for tests
        router.Use(gin.Recovery())

        // Register health endpoints
        router.GET("/healthz", func(c *gin.Context) </span><span class="cov8" title="1">{
                Success(c, gin.H{"status": "ok"})
        }</span>)

        <span class="cov8" title="1">router.GET("/readyz", func(c *gin.Context) </span><span class="cov8" title="1">{
                Success(c, gin.H{"status": "ready"})
        }</span>)

        // Register metrics endpoint
        <span class="cov8" title="1">metrics.RegisterMetricsEndpoint(router)

        // Register webhook routes without middleware
        webhookHandler := NewWebhookHandler(container)
        router.POST("/webhook", webhookHandler.HandleWebhook)</span>
}</pre>
		
		<pre class="file" id="file5" style="display: none">package api

import (
        "context"
        "fmt"
        "io"
        "log/slog"

        "github.com/gin-gonic/gin"
        daprclient "github.com/dapr/go-sdk/client"
        "go.opentelemetry.io/otel"
        "go.opentelemetry.io/otel/attribute"
        "go.opentelemetry.io/otel/codes"
        "webhook_microservice/internal/app"
        "webhook_microservice/internal/app/commands"
        "webhook_microservice/internal/api/middleware"
        "webhook_microservice/internal/container"
        "webhook_microservice/internal/domain"
        "webhook_microservice/internal/infra/dapr"
)

// WebhookHandler handles HTTP requests for webhooks
type WebhookHandler struct {
        webhookHandler   *commands.WebhookHandler
        dedupService     app.DeduplicationService
        publishService   app.PublishingService
        logger           *slog.Logger
}

// NewWebhookHandler creates a new WebhookHandler
func NewWebhookHandler(container *container.Container) *WebhookHandler <span class="cov8" title="1">{
        // Create state client and deduplication service using proper Dapr infrastructure
        stateClient := dapr.NewStateClient(container.DaprClient())

        // Use hardcoded store name for now since config loading has issues
        storeName := container.Config().StateStoreName
        if storeName == "" </span><span class="cov8" title="1">{
                storeName = "statestore"
        }</span>
        <span class="cov8" title="1">fmt.Printf("DEBUG: Using StateStoreName: '%s'\n", storeName)
        dedupService := dapr.NewWebhookDeduplicationService(stateClient, storeName)

        // Create simple publishing service using Dapr pub/sub API directly
        // Fix empty pubsubName and topicName issues
        pubsubName := container.Config().PubSubName
        if pubsubName == "" </span><span class="cov8" title="1">{
                pubsubName = "redis-pubsub"
        }</span>
        <span class="cov8" title="1">topicName := container.Config().TopicName
        if topicName == "" </span><span class="cov8" title="1">{
                topicName = "webhook-events"
        }</span>
        <span class="cov8" title="1">fmt.Printf("DEBUG: Using PubSubName: '%s', TopicName: '%s'\n", pubsubName, topicName)

        publishService := &amp;SimplePubService{
                daprClient: container.DaprClient(),
                pubsubName: pubsubName,
                topicName:  topicName,
                logger:     container.Logger(),
        }

        // Create webhook handler
        webhookHandler := commands.NewWebhookHandler(
                dedupService,
                publishService,
                container.Logger(),
        )

        return &amp;WebhookHandler{
                webhookHandler: webhookHandler,
                dedupService:   dedupService,
                publishService: publishService,
                logger:         container.Logger(),
        }</span>
}

// RegisterRoutes registers the webhook routes
func (h *WebhookHandler) RegisterRoutes(router *gin.Engine) <span class="cov0" title="0">{
        // Configure WAF middleware
        wafConfig := middleware.WAFConfig{
                AllowList:    []string{"0.0.0.0/0", "::/0"}, // Allow all IPv4 and IPv6 IPs in development
                RateLimitRPM: 1000,                           // 1000 requests per minute
        }

        // Configure validator middleware
        validatorConfig := middleware.ValidatorConfig{
                RequiredHeaders: []string{"X-Delivery-ID"},
        }

        // Add webhook-specific middleware
        webhookGroup := router.Group("/webhook")
        webhookGroup.Use(
                middleware.WAFMiddleware(wafConfig),
                middleware.ValidatorMiddleware(validatorConfig),
                middleware.SignatureValidatorMiddleware(),
        )
        </span><span class="cov0" title="0">{
                webhookGroup.POST("", h.HandleWebhook)
        }</span>
}



// HandleWebhook handles POST /webhook
func (h *WebhookHandler) HandleWebhook(c *gin.Context) <span class="cov8" title="1">{
        // Start tracing span using shared tracing
        tracer := otel.Tracer("webhook-microservice")
        ctx, span := tracer.Start(c.Request.Context(), "webhook.handle")
        defer span.End()

        // Get delivery ID from context (set by middleware)
        deliveryID, exists := c.Get("delivery_id")
        if !exists </span><span class="cov8" title="1">{
                span.RecordError(fmt.Errorf("missing delivery ID"))
                BadRequest(c, "Missing delivery ID")
                return
        }</span>

        // Add delivery ID to span
        <span class="cov0" title="0">span.SetAttributes(attribute.String("webhook.delivery_id", deliveryID.(string)))

        // Get source from headers or use a default
        source := c.GetHeader("X-Source")
        if source == "" </span><span class="cov0" title="0">{
                source = c.GetHeader("User-Agent")
                if source == "" </span><span class="cov0" title="0">{
                        source = "unknown"
                }</span>
        }

        // Read the payload
        <span class="cov0" title="0">payload, err := io.ReadAll(c.Request.Body)
        if err != nil </span><span class="cov0" title="0">{
                h.logger.Error("Failed to read request body", "error", err)
                BadRequest(c, "Failed to read request body")
                return
        }</span>

        <span class="cov0" title="0">if len(payload) == 0 </span><span class="cov0" title="0">{
                BadRequest(c, "Empty payload")
                return
        }</span>

        // Create command
        <span class="cov0" title="0">cmd := commands.HandleWebhookCommand{
                DeliveryID: deliveryID.(string),
                Source:     source,
                Payload:    payload,
        }

        // Handle the webhook with tracing context
        err = h.webhookHandler.HandleWebhook(ctx, cmd)
        if err != nil </span><span class="cov0" title="0">{
                span.RecordError(err)
                span.SetStatus(codes.Error, err.Error())
                statusCode := app.HTTPStatusCode(err)

                switch err </span>{
                case app.ErrDuplicate:<span class="cov0" title="0">
                        // Idempotent response for duplicates
                        span.SetAttributes(attribute.String("webhook.result", "duplicate"))
                        h.logger.Info("Duplicate webhook ignored", "delivery_id", deliveryID, "source", source)
                        Success(c, gin.H{"status": "already_processed", "delivery_id": deliveryID})</span>
                case app.ErrInvalidID, app.ErrInvalidWebhookData:<span class="cov0" title="0">
                        span.SetAttributes(attribute.String("webhook.result", "invalid"))
                        h.logger.Warn("Invalid webhook data", "delivery_id", deliveryID, "source", source, "error", err)
                        JSON(c, statusCode, NewErrorResponse("INVALID_WEBHOOK", err.Error()))</span>
                case app.ErrPublishFailed:<span class="cov0" title="0">
                        span.SetAttributes(attribute.String("webhook.result", "publish_failed"))
                        h.logger.Error("Failed to publish webhook", "delivery_id", deliveryID, "source", source, "error", err)
                        JSON(c, statusCode, NewErrorResponse("PUBLISH_FAILED", "Failed to process webhook"))</span>
                default:<span class="cov0" title="0">
                        span.SetAttributes(attribute.String("webhook.result", "error"))
                        h.logger.Error("Webhook processing failed", "delivery_id", deliveryID, "source", source, "error", err)
                        JSON(c, statusCode, NewErrorResponse("PROCESSING_FAILED", "Internal server error"))</span>
                }
                <span class="cov0" title="0">return</span>
        }

        // Success response
        <span class="cov0" title="0">span.SetAttributes(attribute.String("webhook.result", "success"))
        h.logger.Info("Webhook processed successfully", "delivery_id", deliveryID, "source", source)
        Success(c, gin.H{
                "status":      "processed",
                "delivery_id": deliveryID,
                "source":      source,
        })</span>
}



// SimplePubService implements PublishingService using Dapr pub/sub API directly
type SimplePubService struct {
        daprClient daprclient.Client
        pubsubName string
        topicName  string
        logger     *slog.Logger
}

func (s *SimplePubService) Publish(ctx context.Context, event domain.WebhookEvent) error <span class="cov0" title="0">{
        // Create a structured event payload
        eventPayload := map[string]interface{}{
                "delivery_id":  event.DeliveryID.String(),
                "source":       event.Source,
                "payload":      string(event.Payload), // Convert bytes to string for JSON serialization
                "received_at":  event.ReceivedAt,
                "event_type":   "webhook.received",
                "event_key":    event.GetKey(),
        }

        err := s.daprClient.PublishEvent(ctx, s.pubsubName, s.topicName, eventPayload)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to publish webhook event: %w", err)
        }</span>

        <span class="cov0" title="0">return nil</span>
}</pre>
		
		<pre class="file" id="file6" style="display: none">package commands

import (
        "context"
        "log/slog"

        "webhook_microservice/internal/app"
        "webhook_microservice/internal/domain"
)

// HandleWebhookCommand represents the command to handle an incoming webhook
type HandleWebhookCommand struct {
        DeliveryID string `json:"delivery_id"`
        Source     string `json:"source"`
        Payload    []byte `json:"payload"`
}

// WebhookHandler handles webhook processing
type WebhookHandler struct {
        dedupService app.DeduplicationService
        pubService   app.PublishingService
        logger       *slog.Logger
}

// NewWebhookHandler creates a new WebhookHandler
func NewWebhookHandler(
        dedupService app.DeduplicationService,
        pubService app.PublishingService,
        logger *slog.Logger,
) *WebhookHandler <span class="cov8" title="1">{
        return &amp;WebhookHandler{
                dedupService: dedupService,
                pubService:   pubService,
                logger:       logger,
        }
}</span>

// HandleWebhook processes the HandleWebhookCommand
func (h *WebhookHandler) HandleWebhook(ctx context.Context, cmd HandleWebhookCommand) error <span class="cov0" title="0">{
        // Create delivery ID with validation
        id, err := domain.NewDeliveryID(cmd.DeliveryID)
        if err != nil </span><span class="cov0" title="0">{
                h.logger.Warn("Invalid delivery ID", "delivery_id", cmd.DeliveryID, "error", err)
                return app.ErrInvalidID
        }</span>

        // Create webhook event with validation
        <span class="cov0" title="0">event, err := domain.NewWebhookEvent(id, cmd.Source, cmd.Payload)
        if err != nil </span><span class="cov0" title="0">{
                h.logger.Warn("Invalid webhook event", "delivery_id", cmd.DeliveryID, "source", cmd.Source, "error", err)
                return app.ErrInvalidWebhookData
        }</span>

        // Check for duplicates
        <span class="cov0" title="0">isDup, err := h.dedupService.IsDuplicate(ctx, id)
        if err != nil </span><span class="cov0" title="0">{
                h.logger.Error("Deduplication check failed", "delivery_id", id.String(), "error", err)
                return app.ErrDeduplicationFailed
        }</span>

        <span class="cov0" title="0">if isDup </span><span class="cov0" title="0">{
                h.logger.Info("Duplicate webhook event ignored", "delivery_id", id.String(), "source", cmd.Source)
                return app.ErrDuplicate
        }</span>

        // Mark as processed before publishing to prevent race conditions
        <span class="cov0" title="0">if err := h.dedupService.MarkProcessed(ctx, id); err != nil </span><span class="cov0" title="0">{
                h.logger.Error("Failed to mark webhook as processed", "delivery_id", id.String(), "error", err)
                return app.ErrDeduplicationFailed
        }</span>

        // Publish the event
        <span class="cov0" title="0">if err := h.pubService.Publish(ctx, *event); err != nil </span><span class="cov0" title="0">{
                h.logger.Error("Failed to publish webhook event", "delivery_id", id.String(), "source", cmd.Source, "error", err)
                return app.ErrPublishFailed
        }</span>

        <span class="cov0" title="0">h.logger.Info("Webhook event processed successfully", "delivery_id", id.String(), "source", cmd.Source)
        return nil</span>
}</pre>
		
		<pre class="file" id="file7" style="display: none">package app

import (
        "errors"
        "net/http"
)

var (
        // ErrInvalidID is returned when delivery ID is invalid → 400 Bad Request
        ErrInvalidID = errors.New("invalid delivery ID")

        // ErrDuplicate is returned when webhook is duplicate → 200 OK (idempotent)
        ErrDuplicate = errors.New("duplicate webhook event")

        // ErrPublishFailed is returned when publishing fails → 502 Bad Gateway
        ErrPublishFailed = errors.New("failed to publish webhook event")

        // ErrInvalidWebhookData is returned when webhook data is invalid → 400 Bad Request
        ErrInvalidWebhookData = errors.New("invalid webhook data")

        // ErrDeduplicationFailed is returned when deduplication check fails → 500 Internal Server Error
        ErrDeduplicationFailed = errors.New("deduplication check failed")
)

// HTTPStatusCode maps application errors to HTTP status codes
func HTTPStatusCode(err error) int <span class="cov0" title="0">{
        switch err </span>{
        case ErrInvalidID, ErrInvalidWebhookData:<span class="cov0" title="0">
                return http.StatusBadRequest</span>
        case ErrDuplicate:<span class="cov0" title="0">
                return http.StatusOK</span> // Idempotent - already processed
        case ErrPublishFailed:<span class="cov0" title="0">
                return http.StatusBadGateway</span>
        case ErrDeduplicationFailed:<span class="cov0" title="0">
                return http.StatusInternalServerError</span>
        default:<span class="cov0" title="0">
                return http.StatusInternalServerError</span>
        }
}</pre>
		
		<pre class="file" id="file8" style="display: none">package config

import (
        "fmt"
        "log/slog"
        "os"
        "strconv"
        "strings"

        "github.com/spf13/viper"
)

// Config represents the webhook microservice configuration
type Config struct {
        Port           int      `yaml:"port"`
        PubSubName     string   `yaml:"pubsub_name"`
        TopicName      string   `yaml:"topic_name"`
        StateStoreName string   `yaml:"state_store_name"`
        RateLimitRPM   int      `yaml:"rate_limit_rpm"`
        WAFAllowList   []string `yaml:"waf_allow_list"`
}

// LoadConfig loads the webhook microservice configuration
func LoadConfig() (*Config, error) <span class="cov8" title="1">{
        v := viper.New()

        // Set default values
        v.SetDefault("port", 8080)
        v.SetDefault("pubsub_name", "redis-pubsub")
        v.SetDefault("topic_name", "webhook-events")
        v.SetDefault("state_store_name", "statestore")
        v.SetDefault("rate_limit_rpm", 1000)
        v.SetDefault("waf_allow_list", []string{"0.0.0.0/0"})

        // Set config file
        configPath := os.Getenv("CONFIG_PATH")
        if configPath == "" </span><span class="cov8" title="1">{
                configPath = "configs/config.yaml"
        }</span>

        <span class="cov8" title="1">v.SetConfigFile(configPath)
        v.SetConfigType("yaml")
        v.AutomaticEnv()

        // Try to read config file, but don't fail if it doesn't exist
        if err := v.ReadInConfig(); err != nil </span><span class="cov8" title="1">{
                // If config file doesn't exist, use environment variables and defaults
                slog.Info("Config file not found, using environment variables and defaults", "error", err)
        }</span>

        // Override with environment variables
        <span class="cov8" title="1">if port := os.Getenv("WEBHOOK_PORT"); port != "" </span><span class="cov8" title="1">{
                if p, err := strconv.Atoi(port); err == nil </span><span class="cov8" title="1">{
                        v.Set("port", p)
                }</span>
        }
        <span class="cov8" title="1">if pubsub := os.Getenv("WEBHOOK_PUBSUB_NAME"); pubsub != "" </span><span class="cov8" title="1">{
                v.Set("pubsub_name", pubsub)
        }</span>
        <span class="cov8" title="1">if topic := os.Getenv("WEBHOOK_TOPIC_NAME"); topic != "" </span><span class="cov8" title="1">{
                v.Set("topic_name", topic)
        }</span>
        <span class="cov8" title="1">if stateStore := os.Getenv("WEBHOOK_STATE_STORE"); stateStore != "" </span><span class="cov8" title="1">{
                v.Set("state_store_name", stateStore)
        }</span>
        <span class="cov8" title="1">if rateLimit := os.Getenv("WEBHOOK_RATE_LIMIT_RPM"); rateLimit != "" </span><span class="cov0" title="0">{
                if r, err := strconv.Atoi(rateLimit); err == nil </span><span class="cov0" title="0">{
                        v.Set("rate_limit_rpm", r)
                }</span>
        }
        <span class="cov8" title="1">if allowList := os.Getenv("WEBHOOK_WAF_ALLOW_LIST"); allowList != "" </span><span class="cov0" title="0">{
                v.Set("waf_allow_list", strings.Split(allowList, ","))
        }</span>

        <span class="cov8" title="1">var config Config
        if err := v.Unmarshal(&amp;config); err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to unmarshal config: %w", err)
        }</span>

        <span class="cov8" title="1">return &amp;config, nil</span>
}

</pre>
		
		<pre class="file" id="file9" style="display: none">package container

import (
        "context"
        "fmt"
        "log/slog"
        "os"
        "strings"
        "sync"

        "github.com/gin-gonic/gin"
        daprclient "github.com/dapr/go-sdk/client"
        "go.opentelemetry.io/otel/trace"

        "webhook_microservice/internal/config"
        "webhook_microservice/internal/infra/tracing"
)

// Container configuration is now handled by the config package

// Container holds all the application dependencies
type Container struct {
        // Configuration from config package
        config *config.Config

        // Core infrastructure
        logger *slog.Logger
        tracer trace.Tracer

        // Simple Dapr client for webhook functionality
        daprClient daprclient.Client

        // Initialization control
        initOnce sync.Once
        err      error

        // Cleanup functions
        tracerCleanup func()
}

// NewContainer creates a new container with all dependencies wired together
func NewContainer() *Container <span class="cov8" title="1">{
        // Load configuration
        cfg, err := config.LoadConfig()
        if err != nil </span><span class="cov0" title="0">{
                // Use defaults if config loading fails
                cfg = &amp;config.Config{
                        Port:           8080,
                        PubSubName:     "redis-pubsub",
                        TopicName:      "webhook-events",
                        StateStoreName: "statestore",
                        RateLimitRPM:   1000,
                        WAFAllowList:   []string{"0.0.0.0/0"},
                }
        }</span>

        <span class="cov8" title="1">c := &amp;Container{
                config: cfg,
        }

        c.initLogger()
        c.initTracer()
        c.initDaprClient()

        return c</span>
}

// initLogger initializes the structured logger
func (c *Container) initLogger() <span class="cov8" title="1">{
        logLevel := getEnvOrDefault("LOG_LEVEL", "info")
        var level slog.Level
        switch strings.ToLower(logLevel) </span>{
        case "debug":<span class="cov0" title="0">
                level = slog.LevelDebug</span>
        case "warn":<span class="cov0" title="0">
                level = slog.LevelWarn</span>
        case "error":<span class="cov0" title="0">
                level = slog.LevelError</span>
        default:<span class="cov8" title="1">
                level = slog.LevelInfo</span>
        }

        <span class="cov8" title="1">opts := &amp;slog.HandlerOptions{
                Level:     level,
                AddSource: getEnvOrDefault("GIN_MODE", gin.DebugMode) == gin.DebugMode,
        }
        c.logger = slog.New(slog.NewJSONHandler(os.Stdout, opts)).With("service", "webhook-microservice")
        slog.SetDefault(c.logger)</span>
}

// initTracer initializes OpenTelemetry tracing if enabled
func (c *Container) initTracer() <span class="cov8" title="1">{
        jaegerEndpoint := getEnvOrDefault("JAEGER_ENDPOINT", "")
        tracingEnabled := getEnvOrDefault("TRACING_ENABLED", "false") == "true"

        if !tracingEnabled || jaegerEndpoint == "" </span><span class="cov8" title="1">{
                c.logger.Info("Tracing disabled or no Jaeger endpoint configured")
                return
        }</span>

        <span class="cov0" title="0">tracingConfig := tracing.Config{
                ServiceName:    "webhook-microservice",
                JaegerEndpoint: jaegerEndpoint,
                Enabled:        tracingEnabled,
        }

        cleanup, err := tracing.Initialize(tracingConfig)
        if err != nil </span><span class="cov0" title="0">{
                c.logger.Warn("Failed to initialize tracer, continuing without tracing", "error", err)
                return
        }</span>

        <span class="cov0" title="0">c.tracerCleanup = cleanup
        c.logger.Info("Tracer initialized successfully")</span>
}

// initDaprClient initializes a simple Dapr client
func (c *Container) initDaprClient() <span class="cov8" title="1">{
        daprEnabled := getEnvOrDefault("DAPR_ENABLED", "true") == "true"
        if !daprEnabled </span><span class="cov0" title="0">{
                c.logger.Info("Dapr disabled in configuration")
                return
        }</span>

        <span class="cov8" title="1">var err error
        c.daprClient, err = daprclient.NewClient()
        if err != nil </span><span class="cov8" title="1">{
                c.logger.Warn("Failed to initialize Dapr client, continuing without Dapr", "error", err)
                return
        }</span>

        <span class="cov0" title="0">c.logger.Info("Dapr client initialized successfully")</span>
}

// Accessor methods
func (c *Container) Logger() *slog.Logger       <span class="cov8" title="1">{ return c.logger }</span>
func (c *Container) Config() *config.Config     <span class="cov8" title="1">{ return c.config }</span>
func (c *Container) DaprClient() daprclient.Client <span class="cov8" title="1">{ return c.daprClient }</span>

// HealthCheck performs a health check on all available components
func (c *Container) HealthCheck(ctx context.Context) error <span class="cov0" title="0">{
        // Simple health check - just verify the container is initialized
        if c.logger == nil </span><span class="cov0" title="0">{
                return fmt.Errorf("logger not initialized")
        }</span>

        // Check Dapr if available
        <span class="cov0" title="0">if c.daprClient != nil </span><span class="cov0" title="0">{
                // Simple Dapr health check by trying to get state
                _, err := c.daprClient.GetState(ctx, c.config.StateStoreName, "health-check", nil)
                if err != nil </span><span class="cov0" title="0">{
                        c.logger.Debug("Dapr health check completed", "error", err)
                }</span>
        }

        <span class="cov0" title="0">return nil</span>
}

// Close closes all connections and resources
func (c *Container) Close(ctx context.Context) error <span class="cov0" title="0">{
        // Close tracer
        if c.tracerCleanup != nil </span><span class="cov0" title="0">{
                c.tracerCleanup()
        }</span>

        // Close Dapr client
        <span class="cov0" title="0">if c.daprClient != nil </span><span class="cov0" title="0">{
                c.daprClient.Close()
        }</span>

        <span class="cov0" title="0">c.logger.Info("Container closed successfully")
        return nil</span>
}

// getEnvOrDefault returns environment variable value or default if not set
func getEnvOrDefault(key, defaultValue string) string <span class="cov8" title="1">{
        if value := os.Getenv(key); value != "" </span><span class="cov8" title="1">{
                return value
        }</span>
        <span class="cov8" title="1">return defaultValue</span>
} </pre>
		
		<pre class="file" id="file10" style="display: none">package domain

import (
        "fmt"
        "strings"
        "time"
)

// WebhookEvent represents an HTTP payload received from a third-party system
type WebhookEvent struct {
        DeliveryID DeliveryID `json:"delivery_id"`
        Source     string     `json:"source"`
        Payload    []byte     `json:"payload"`
        ReceivedAt time.Time  `json:"received_at"`
}

// DeliveryID is a unique identifier for a webhook event
type DeliveryID struct {
        ID string `json:"id"`
}

// ReceivedAt represents when a webhook event was received
type ReceivedAt struct {
        Time time.Time `json:"time"`
}

// NewDeliveryID creates a new DeliveryID with validation
func NewDeliveryID(raw string) (DeliveryID, error) <span class="cov0" title="0">{
        if raw == "" </span><span class="cov0" title="0">{
                return DeliveryID{}, ErrInvalidDeliveryID
        }</span>

        // Trim whitespace and validate length
        <span class="cov0" title="0">id := strings.TrimSpace(raw)
        if len(id) == 0 </span><span class="cov0" title="0">{
                return DeliveryID{}, ErrInvalidDeliveryID
        }</span>

        // Basic validation - delivery IDs should be reasonable length
        <span class="cov0" title="0">if len(id) &gt; 255 </span><span class="cov0" title="0">{
                return DeliveryID{}, ErrDeliveryIDTooLong
        }</span>

        <span class="cov0" title="0">return DeliveryID{ID: id}, nil</span>
}

// String returns the string representation of the DeliveryID
func (d DeliveryID) String() string <span class="cov0" title="0">{
        return d.ID
}</span>

// IsEmpty checks if the DeliveryID is empty
func (d DeliveryID) IsEmpty() bool <span class="cov0" title="0">{
        return d.ID == ""
}</span>

// NewReceivedAt creates a new ReceivedAt with current UTC time
func NewReceivedAt() ReceivedAt <span class="cov0" title="0">{
        return ReceivedAt{Time: time.Now().UTC()}
}</span>

// NewWebhookEvent creates a new WebhookEvent with validation
func NewWebhookEvent(deliveryID DeliveryID, source string, payload []byte) (*WebhookEvent, error) <span class="cov0" title="0">{
        if deliveryID.IsEmpty() </span><span class="cov0" title="0">{
                return nil, ErrInvalidDeliveryID
        }</span>

        <span class="cov0" title="0">if source == "" </span><span class="cov0" title="0">{
                return nil, ErrInvalidSource
        }</span>

        <span class="cov0" title="0">if len(payload) == 0 </span><span class="cov0" title="0">{
                return nil, ErrEmptyPayload
        }</span>

        // Validate source format (basic validation)
        <span class="cov0" title="0">source = strings.TrimSpace(source)
        if len(source) == 0 </span><span class="cov0" title="0">{
                return nil, ErrInvalidSource
        }</span>

        <span class="cov0" title="0">return &amp;WebhookEvent{
                DeliveryID: deliveryID,
                Source:     source,
                Payload:    payload,
                ReceivedAt: NewReceivedAt().Time,
        }, nil</span>
}

// Validate validates the webhook event invariants
func (w *WebhookEvent) Validate() error <span class="cov0" title="0">{
        if w.DeliveryID.IsEmpty() </span><span class="cov0" title="0">{
                return ErrInvalidDeliveryID
        }</span>

        <span class="cov0" title="0">if w.Source == "" </span><span class="cov0" title="0">{
                return ErrInvalidSource
        }</span>

        <span class="cov0" title="0">if len(w.Payload) == 0 </span><span class="cov0" title="0">{
                return ErrEmptyPayload
        }</span>

        <span class="cov0" title="0">if w.ReceivedAt.IsZero() </span><span class="cov0" title="0">{
                return ErrInvalidReceivedAt
        }</span>

        <span class="cov0" title="0">return nil</span>
}

// GetKey returns a unique key for this webhook event (for deduplication)
func (w *WebhookEvent) GetKey() string <span class="cov0" title="0">{
        return fmt.Sprintf("%s:%s", w.Source, w.DeliveryID.String())
}</pre>
		
		<pre class="file" id="file11" style="display: none">package dapr

import (
        "context"
        "fmt"
        "net/http"

        dapr "github.com/dapr/go-sdk/client"
        "github.com/gin-gonic/gin"
        "go.uber.org/zap"
        "webhook_microservice/internal/domain"
)

// PubSubClient handles Dapr pub/sub operations
type PubSubClient struct {
        client dapr.Client
        logger *zap.Logger
}

// NewPubSubClient creates a new Dapr pub/sub client
func NewPubSubClient(client dapr.Client, logger *zap.Logger) *PubSubClient <span class="cov0" title="0">{
        return &amp;PubSubClient{
                client: client,
                logger: logger,
        }
}</span>

// PublishEvent publishes an event to a topic
func (p *PubSubClient) PublishEvent(ctx context.Context, pubsubName, topic string, data interface{}) error <span class="cov0" title="0">{
        if p.client == nil </span><span class="cov0" title="0">{
                return fmt.Errorf("dapr client not initialized")
        }</span>

        <span class="cov0" title="0">err := p.client.PublishEvent(ctx, pubsubName, topic, data)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to publish event: %w", err)
        }</span>

        <span class="cov0" title="0">p.logger.Info("Event published successfully",
                zap.String("pubsub", pubsubName),
                zap.String("topic", topic),
        )

        return nil</span>
}

// SubscriptionHandler represents a subscription event handler
type SubscriptionHandler func(ctx context.Context, event *SubscriptionEvent) error

// SubscriptionEvent represents a Dapr subscription event
type SubscriptionEvent struct {
        ID              string                 `json:"id"`
        Source          string                 `json:"source"`
        Type            string                 `json:"type"`
        SpecVersion     string                 `json:"specversion"`
        DataContentType string                 `json:"datacontenttype"`
        Data            map[string]interface{} `json:"data"`
        Topic           string                 `json:"topic"`
        PubsubName      string                 `json:"pubsubname"`
}

// RegisterSubscriptionHandler registers a subscription handler for Gin router
func (p *PubSubClient) RegisterSubscriptionHandler(router *gin.Engine, topic, pubsubName string, handler SubscriptionHandler) <span class="cov0" title="0">{
        router.POST(fmt.Sprintf("/dapr/subscribe/%s", topic), func(c *gin.Context) </span><span class="cov0" title="0">{
                var event SubscriptionEvent
                if err := c.ShouldBindJSON(&amp;event); err != nil </span><span class="cov0" title="0">{
                        p.logger.Error("Failed to bind subscription event", zap.Error(err))
                        c.JSON(http.StatusBadRequest, gin.H{"error": "invalid event format"})
                        return
                }</span>

                <span class="cov0" title="0">event.Topic = topic
                event.PubsubName = pubsubName

                ctx := c.Request.Context()
                if err := handler(ctx, &amp;event); err != nil </span><span class="cov0" title="0">{
                        p.logger.Error("Subscription handler failed",
                                zap.Error(err),
                                zap.String("topic", topic),
                                zap.String("pubsub", pubsubName),
                        )
                        c.JSON(http.StatusInternalServerError, gin.H{"error": "handler failed"})
                        return
                }</span>

                <span class="cov0" title="0">c.JSON(http.StatusOK, gin.H{"status": "SUCCESS"})</span>
        })
}

// GetSubscriptions returns the subscription configuration for Dapr
func (p *PubSubClient) GetSubscriptions() []map[string]interface{} <span class="cov0" title="0">{
        return []map[string]interface{}{
                {
                        "pubsubname": "redis-pubsub",
                        "topic":      "template-events",
                        "route":      "/dapr/subscribe/template-events",
                },
                {
                        "pubsubname": "redis-pubsub", 
                        "topic":      "notifications",
                        "route":      "/dapr/subscribe/notifications",
                },
        }
}</span>

// RegisterSubscriptionsEndpoint registers the /dapr/subscribe endpoint
func (p *PubSubClient) RegisterSubscriptionsEndpoint(router *gin.Engine) <span class="cov0" title="0">{
        router.GET("/dapr/subscribe", func(c *gin.Context) </span><span class="cov0" title="0">{
                c.JSON(http.StatusOK, p.GetSubscriptions())
        }</span>)
}

// WebhookPublishingService implements PublishingService using Dapr pub/sub
type WebhookPublishingService struct {
        client     *PubSubClient
        pubsubName string
        topicName  string
}

// NewWebhookPublishingService creates a new webhook publishing service
func NewWebhookPublishingService(client *PubSubClient, pubsubName, topicName string) *WebhookPublishingService <span class="cov0" title="0">{
        return &amp;WebhookPublishingService{
                client:     client,
                pubsubName: pubsubName,
                topicName:  topicName,
        }
}</span>

// Publish publishes a webhook event to the configured topic
func (w *WebhookPublishingService) Publish(ctx context.Context, event domain.WebhookEvent) error <span class="cov0" title="0">{
        // Create a structured event payload
        eventPayload := map[string]interface{}{
                "delivery_id":  event.DeliveryID.String(),
                "source":       event.Source,
                "payload":      string(event.Payload), // Convert bytes to string for JSON serialization
                "received_at":  event.ReceivedAt,
                "event_type":   "webhook.received",
                "event_key":    event.GetKey(),
        }

        err := w.client.PublishEvent(ctx, w.pubsubName, w.topicName, eventPayload)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to publish webhook event: %w", err)
        }</span>

        <span class="cov0" title="0">return nil</span>
}</pre>
		
		<pre class="file" id="file12" style="display: none">package dapr

import (
        "bytes"
        "context"
        "encoding/json"
        "fmt"
        "io"
        "net/http"
        "time"

        dapr "github.com/dapr/go-sdk/client"
        "webhook_microservice/internal/domain"
)

// StateClient handles Dapr state management operations
type StateClient struct {
        client dapr.Client
}

// NewStateClient creates a new Dapr state client
func NewStateClient(client dapr.Client) *StateClient <span class="cov8" title="1">{
        return &amp;StateClient{
                client: client,
        }
}</span>

// GetState retrieves a state from Dapr state store using HTTP API as workaround
func (s *StateClient) GetState(ctx context.Context, storeName, key string) ([]byte, error) <span class="cov0" title="0">{
        if s.client == nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("dapr client not initialized")
        }</span>

        // Use HTTP API as workaround for Go SDK issue
        // Fix empty storeName issue
        <span class="cov0" title="0">if storeName == "" </span><span class="cov0" title="0">{
                storeName = "statestore"
        }</span>
        <span class="cov0" title="0">url := fmt.Sprintf("http://localhost:3500/v1.0/state/%s/%s", storeName, key)
        fmt.Printf("DEBUG: Making GET request to URL: %s\n", url)

        // Create HTTP request with proper context
        req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to create HTTP request: %w", err)
        }</span>
        <span class="cov0" title="0">req.Header.Set("dapr-app-id", "webhook-microservice")

        client := &amp;http.Client{}
        resp, err := client.Do(req)
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to get state via HTTP: %w", err)
        }</span>
        <span class="cov0" title="0">defer resp.Body.Close()

        if resp.StatusCode == 404 || resp.StatusCode == 204 </span><span class="cov0" title="0">{
                // Key doesn't exist, return empty (404 = not found, 204 = no content)
                return []byte{}, nil
        }</span>

        <span class="cov0" title="0">if resp.StatusCode != 200 </span><span class="cov0" title="0">{
                // Read response body for debugging
                body, _ := io.ReadAll(resp.Body)
                return nil, fmt.Errorf("HTTP error: %d, body: %s", resp.StatusCode, string(body))
        }</span>

        <span class="cov0" title="0">body, err := io.ReadAll(resp.Body)
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to read response: %w", err)
        }</span>

        // Remove JSON quotes if present
        <span class="cov0" title="0">if len(body) &gt;= 2 &amp;&amp; body[0] == '"' &amp;&amp; body[len(body)-1] == '"' </span><span class="cov0" title="0">{
                body = body[1 : len(body)-1]
        }</span>

        <span class="cov0" title="0">return body, nil</span>
}

// SaveState saves a state to Dapr state store using HTTP API as workaround
func (s *StateClient) SaveState(ctx context.Context, storeName, key string, value interface{}) error <span class="cov0" title="0">{
        if s.client == nil </span><span class="cov0" title="0">{
                return fmt.Errorf("dapr client not initialized")
        }</span>

        // Use HTTP API as workaround for Go SDK issue
        // Fix empty storeName issue
        <span class="cov0" title="0">if storeName == "" </span><span class="cov0" title="0">{
                storeName = "statestore"
        }</span>
        <span class="cov0" title="0">url := fmt.Sprintf("http://localhost:3500/v1.0/state/%s", storeName)
        fmt.Printf("DEBUG: Making POST request to URL: %s\n", url)

        // Create the state array payload
        stateData := []map[string]interface{}{
                {
                        "key":   key,
                        "value": value,
                },
        }

        data, err := json.Marshal(stateData)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to marshal state data: %w", err)
        }</span>

        // Create HTTP request with proper context and headers
        <span class="cov0" title="0">req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(data))
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to create HTTP request: %w", err)
        }</span>
        <span class="cov0" title="0">req.Header.Set("Content-Type", "application/json")
        req.Header.Set("dapr-app-id", "webhook-microservice")

        client := &amp;http.Client{}
        resp, err := client.Do(req)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to save state via HTTP: %w", err)
        }</span>
        <span class="cov0" title="0">defer resp.Body.Close()

        if resp.StatusCode != 204 &amp;&amp; resp.StatusCode != 200 </span><span class="cov0" title="0">{
                // Read response body for debugging
                body, _ := io.ReadAll(resp.Body)
                return fmt.Errorf("HTTP error: %d, body: %s", resp.StatusCode, string(body))
        }</span>

        <span class="cov0" title="0">return nil</span>
}

// DeleteState deletes a state from Dapr state store
func (s *StateClient) DeleteState(ctx context.Context, storeName, key string) error <span class="cov0" title="0">{
        if s.client == nil </span><span class="cov0" title="0">{
                return fmt.Errorf("dapr client not initialized")
        }</span>

        <span class="cov0" title="0">err := s.client.DeleteState(ctx, storeName, key, nil)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to delete state: %w", err)
        }</span>

        <span class="cov0" title="0">return nil</span>
}

// GetStateWithMetadata retrieves state with metadata
func (s *StateClient) GetStateWithMetadata(ctx context.Context, storeName, key string) (*dapr.StateItem, error) <span class="cov0" title="0">{
        if s.client == nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("dapr client not initialized")
        }</span>

        <span class="cov0" title="0">item, err := s.client.GetState(ctx, storeName, key, nil)
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to get state with metadata: %w", err)
        }</span>

        <span class="cov0" title="0">return item, nil</span>
}

// WebhookDeduplicationService implements DeduplicationService using Dapr state store
type WebhookDeduplicationService struct {
        client    *StateClient
        storeName string
}

// NewWebhookDeduplicationService creates a new webhook deduplication service
func NewWebhookDeduplicationService(client *StateClient, storeName string) *WebhookDeduplicationService <span class="cov8" title="1">{
        return &amp;WebhookDeduplicationService{
                client:    client,
                storeName: storeName,
        }
}</span>

// IsDuplicate checks if a delivery ID has already been processed
func (w *WebhookDeduplicationService) IsDuplicate(ctx context.Context, id domain.DeliveryID) (bool, error) <span class="cov0" title="0">{
        key := fmt.Sprintf("webhook:processed:%s", id.String())

        data, err := w.client.GetState(ctx, w.storeName, key)
        if err != nil </span><span class="cov0" title="0">{
                return false, fmt.Errorf("failed to check duplicate: %w", err)
        }</span>

        // If data exists, it's a duplicate
        <span class="cov0" title="0">return len(data) &gt; 0, nil</span>
}

// MarkProcessed marks a delivery ID as processed
func (w *WebhookDeduplicationService) MarkProcessed(ctx context.Context, id domain.DeliveryID) error <span class="cov0" title="0">{
        key := fmt.Sprintf("webhook:processed:%s", id.String())

        // Store a simple timestamp string to mark as processed
        processedAt := time.Now().UTC().Format(time.RFC3339)

        err := w.client.SaveState(ctx, w.storeName, key, processedAt)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to mark as processed: %w", err)
        }</span>

        <span class="cov0" title="0">return nil</span>
}</pre>
		
		<pre class="file" id="file13" style="display: none">package tracing

import (
        "github.com/Matrics-io/the-manhattan-project-huly/go/shared/tracing"
)

// Config wraps the shared tracing config for webhook microservice
type Config struct {
        ServiceName    string
        JaegerEndpoint string
        Enabled        bool
}

// Initialize sets up tracing using the shared tracing package
func Initialize(cfg Config) (func(), error) <span class="cov0" title="0">{
        sharedConfig := tracing.Config{
                ServiceName:    cfg.ServiceName,
                JaegerEndpoint: cfg.JaegerEndpoint,
                Enabled:        cfg.Enabled,
        }
        
        return tracing.Initialize(sharedConfig)
}</span>

// Middleware returns the shared tracing middleware
func Middleware(serviceName string) func() <span class="cov0" title="0">{
        return func() </span>{<span class="cov0" title="0">
                // The shared tracing middleware is applied in the router
                // This is just a placeholder for webhook-specific tracing setup
        }</span>
}
</pre>
		
		</div>
	</body>
	<script>
	(function() {
		var files = document.getElementById('files');
		var visible;
		files.addEventListener('change', onChange, false);
		function select(part) {
			if (visible)
				visible.style.display = 'none';
			visible = document.getElementById(part);
			if (!visible)
				return;
			files.value = part;
			visible.style.display = 'block';
			location.hash = part;
		}
		function onChange() {
			select(files.value);
			window.scrollTo(0, 0);
		}
		if (location.hash != "") {
			select(location.hash.substr(1));
		}
		if (!visible) {
			select("file0");
		}
	})();
	</script>
</html>
