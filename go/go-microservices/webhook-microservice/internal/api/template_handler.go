package api

import (
	"log/slog"

	"github.com/gin-gonic/gin"
	"template_microservice/internal/app"
	"template_microservice/internal/infra/dapr"
)

// Template<PERSON>andler handles HTTP requests for templates
type TemplateHandler struct {
	templateService *app.TemplateService
	pubsubClient    *dapr.PubSubClient
	logger          *slog.Logger
}

// NewTemplateHandler creates a new TemplateHandler
func NewTemplateHandler(
	templateService *app.TemplateService,
	pubsubClient *dapr.PubSubClient,
	logger *slog.Logger,
) *TemplateHandler {
	return &TemplateHandler{
		templateService: templateService,
		pubsubClient:    pubsubClient,
		logger:          logger,
	}
}

// RegisterRoutes registers the template routes
func (h *TemplateHandler) RegisterRoutes(router *gin.Engine) {
	templates := router.Group("/api/v1/templates")
	{
		templates.POST("", h.Create)
		templates.GET("/:id", h.GetByID)
	}
}

// <PERSON><PERSON> handles POST /api/v1/templates
func (h *TemplateHandler) Create(c *gin.Context) {
	var req struct {
		Name    string `json:"name" binding:"required"`
		Content string `json:"content" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		BadRequest(c, "Invalid request body")
		return
	}

	cmd := app.CreateTemplateCommand{
		Name:    req.Name,
		Content: req.Content,
	}

	template, err := h.templateService.CreateTemplate(c.Request.Context(), cmd)
	if err != nil {
		switch err {
		case app.ErrTemplateAlreadyExists:
			BadRequest(c, "Template already exists")
		default:
			h.logger.Error("Failed to create template", "error", err)
			InternalError(c, "Failed to create template")
		}
		return
	}

	// Note: Event publishing is now handled inside the service
	Created(c, template)
}

// GetByID handles GET /api/v1/templates/:id
func (h *TemplateHandler) GetByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		BadRequest(c, "Template ID is required")
		return
	}

	query := app.GetTemplateQuery{ID: id}
	template, err := h.templateService.GetTemplate(query)
	if err != nil {
		switch err {
		case app.ErrTemplateNotFound:
			NotFound(c, "Template not found")
		default:
			h.logger.Error("Failed to get template", "error", err)
			InternalError(c, "Failed to get template")
		}
		return
	}

	Success(c, template)
} 