package api

import (
	"context"
	"fmt"
	"io"
	"log/slog"
	"time"

	"github.com/gin-gonic/gin"
	daprclient "github.com/dapr/go-sdk/client"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"webhook_microservice/internal/app"
	"webhook_microservice/internal/app/commands"
	"webhook_microservice/internal/api/middleware"
	"webhook_microservice/internal/container"
	"webhook_microservice/internal/domain"
)

// WebhookHandler handles HTTP requests for webhooks
type WebhookHandler struct {
	webhookHandler   *commands.WebhookHandler
	dedupService     app.DeduplicationService
	publishService   app.PublishingService
	logger           *slog.Logger
}

// NewWebhookHandler creates a new WebhookHandler
func NewWebhookHandler(container *container.Container) *WebhookHandler {
	// Create simple deduplication service using Dapr state API directly
	dedupService := &SimpleDedupService{
		daprClient:    container.DaprClient(),
		stateStore:    container.Config().StateStoreName,
		logger:        container.Logger(),
	}

	// Create simple publishing service using Dapr pub/sub API directly
	publishService := &SimplePubService{
		daprClient: container.DaprClient(),
		pubsubName: container.Config().PubSubName,
		topicName:  container.Config().TopicName,
		logger:     container.Logger(),
	}

	// Create webhook handler
	webhookHandler := commands.NewWebhookHandler(
		dedupService,
		publishService,
		container.Logger(),
	)

	return &WebhookHandler{
		webhookHandler: webhookHandler,
		dedupService:   dedupService,
		publishService: publishService,
		logger:         container.Logger(),
	}
}

// RegisterRoutes registers the webhook routes
func (h *WebhookHandler) RegisterRoutes(router *gin.Engine) {
	// Configure WAF middleware
	wafConfig := middleware.WAFConfig{
		AllowList:    []string{"0.0.0.0/0"}, // Allow all IPs in development
		RateLimitRPM: 1000,                   // 1000 requests per minute
	}

	// Configure validator middleware
	validatorConfig := middleware.ValidatorConfig{
		RequiredHeaders: []string{"X-Delivery-ID"},
	}

	// Add webhook-specific middleware
	webhookGroup := router.Group("/webhook")
	webhookGroup.Use(
		middleware.WAFMiddleware(wafConfig),
		middleware.ValidatorMiddleware(validatorConfig),
		middleware.SignatureValidatorMiddleware(),
	)
	{
		webhookGroup.POST("", h.HandleWebhook)
	}
}



// HandleWebhook handles POST /webhook
func (h *WebhookHandler) HandleWebhook(c *gin.Context) {
	// Start tracing span using shared tracing
	tracer := otel.Tracer("webhook-microservice")
	ctx, span := tracer.Start(c.Request.Context(), "webhook.handle")
	defer span.End()

	// Get delivery ID from context (set by middleware)
	deliveryID, exists := c.Get("delivery_id")
	if !exists {
		span.RecordError(fmt.Errorf("missing delivery ID"))
		BadRequest(c, "Missing delivery ID")
		return
	}

	// Add delivery ID to span
	span.SetAttributes(attribute.String("webhook.delivery_id", deliveryID.(string)))

	// Get source from headers or use a default
	source := c.GetHeader("X-Source")
	if source == "" {
		source = c.GetHeader("User-Agent")
		if source == "" {
			source = "unknown"
		}
	}

	// Read the payload
	payload, err := io.ReadAll(c.Request.Body)
	if err != nil {
		h.logger.Error("Failed to read request body", "error", err)
		BadRequest(c, "Failed to read request body")
		return
	}

	if len(payload) == 0 {
		BadRequest(c, "Empty payload")
		return
	}

	// Create command
	cmd := commands.HandleWebhookCommand{
		DeliveryID: deliveryID.(string),
		Source:     source,
		Payload:    payload,
	}

	// Handle the webhook with tracing context
	err = h.webhookHandler.HandleWebhook(ctx, cmd)
	if err != nil {
		span.RecordError(err)
		span.SetStatus(codes.Error, err.Error())
		statusCode := app.HTTPStatusCode(err)

		switch err {
		case app.ErrDuplicate:
			// Idempotent response for duplicates
			span.SetAttributes(attribute.String("webhook.result", "duplicate"))
			h.logger.Info("Duplicate webhook ignored", "delivery_id", deliveryID, "source", source)
			Success(c, gin.H{"status": "already_processed", "delivery_id": deliveryID})
		case app.ErrInvalidID, app.ErrInvalidWebhookData:
			span.SetAttributes(attribute.String("webhook.result", "invalid"))
			h.logger.Warn("Invalid webhook data", "delivery_id", deliveryID, "source", source, "error", err)
			JSON(c, statusCode, NewErrorResponse("INVALID_WEBHOOK", err.Error()))
		case app.ErrPublishFailed:
			span.SetAttributes(attribute.String("webhook.result", "publish_failed"))
			h.logger.Error("Failed to publish webhook", "delivery_id", deliveryID, "source", source, "error", err)
			JSON(c, statusCode, NewErrorResponse("PUBLISH_FAILED", "Failed to process webhook"))
		default:
			span.SetAttributes(attribute.String("webhook.result", "error"))
			h.logger.Error("Webhook processing failed", "delivery_id", deliveryID, "source", source, "error", err)
			JSON(c, statusCode, NewErrorResponse("PROCESSING_FAILED", "Internal server error"))
		}
		return
	}

	// Success response
	span.SetAttributes(attribute.String("webhook.result", "success"))
	h.logger.Info("Webhook processed successfully", "delivery_id", deliveryID, "source", source)
	Success(c, gin.H{
		"status":      "processed",
		"delivery_id": deliveryID,
		"source":      source,
	})
}

// SimpleDedupService implements DeduplicationService using Dapr state API directly
type SimpleDedupService struct {
	daprClient daprclient.Client
	stateStore string
	logger     *slog.Logger
}

func (s *SimpleDedupService) IsDuplicate(ctx context.Context, id domain.DeliveryID) (bool, error) {
	key := fmt.Sprintf("webhook:processed:%s", id.String())

	item, err := s.daprClient.GetState(ctx, s.stateStore, key, nil)
	if err != nil {
		return false, fmt.Errorf("failed to check duplicate: %w", err)
	}

	// If data exists, it's a duplicate
	return len(item.Value) > 0, nil
}

func (s *SimpleDedupService) MarkProcessed(ctx context.Context, id domain.DeliveryID) error {
	key := fmt.Sprintf("webhook:processed:%s", id.String())

	// Store a simple timestamp to mark as processed
	processedAt := time.Now().UTC().Format(time.RFC3339)

	err := s.daprClient.SaveState(ctx, s.stateStore, key, []byte(processedAt), nil)
	if err != nil {
		return fmt.Errorf("failed to mark as processed: %w", err)
	}

	return nil
}

// SimplePubService implements PublishingService using Dapr pub/sub API directly
type SimplePubService struct {
	daprClient daprclient.Client
	pubsubName string
	topicName  string
	logger     *slog.Logger
}

func (s *SimplePubService) Publish(ctx context.Context, event domain.WebhookEvent) error {
	// Create a structured event payload
	eventPayload := map[string]interface{}{
		"delivery_id":  event.DeliveryID.String(),
		"source":       event.Source,
		"payload":      string(event.Payload), // Convert bytes to string for JSON serialization
		"received_at":  event.ReceivedAt,
		"event_type":   "webhook.received",
		"event_key":    event.GetKey(),
	}

	err := s.daprClient.PublishEvent(ctx, s.pubsubName, s.topicName, eventPayload)
	if err != nil {
		return fmt.Errorf("failed to publish webhook event: %w", err)
	}

	return nil
}