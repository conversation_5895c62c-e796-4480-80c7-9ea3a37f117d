package api

import (
	"io"
	"log/slog"
	"net/http"

	"github.com/gin-gonic/gin"
	"webhook_microservice/internal/app"
	"webhook_microservice/internal/container"
	"webhook_microservice/internal/infra/dapr"
)

// WebhookHandler handles HTTP requests for webhooks
type WebhookHandler struct {
	webhookHandler   *app.WebhookHandler
	dedupService     app.DeduplicationService
	publishService   app.PublishingService
	logger           *slog.Logger
}

// NewWebhookHandler creates a new WebhookHandler
func NewWebhookHandler(container *container.Container) *WebhookHandler {
	// Create deduplication service
	dedupService := dapr.NewWebhookDeduplicationService(
		container.StateClient(),
		container.Config().DaprStateStore,
	)

	// Create publishing service
	publishService := dapr.NewWebhookPublishingService(
		container.PubSubClient(),
		container.Config().DaprPubSubName,
		"webhook-events", // topic name
	)

	// Create webhook handler
	webhookHandler := app.NewWebhookHandler(
		dedupService,
		publishService,
		container.Logger(),
	)

	return &WebhookHandler{
		webhookHandler: webhookHandler,
		dedupService:   dedupService,
		publishService: publishService,
		logger:         container.Logger(),
	}
}

// RegisterRoutes registers the webhook routes
func (h *WebhookHandler) RegisterRoutes(router *gin.Engine) {
	// Add webhook-specific middleware
	webhookGroup := router.Group("/webhook")
	webhookGroup.Use(h.validateWebhookHeaders())
	{
		webhookGroup.POST("", h.HandleWebhook)
	}
}

// validateWebhookHeaders middleware validates required webhook headers
func (h *WebhookHandler) validateWebhookHeaders() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check for required delivery ID header
		deliveryID := c.GetHeader("X-Delivery-ID")
		if deliveryID == "" {
			// Try alternative header names
			deliveryID = c.GetHeader("X-GitHub-Delivery")
			if deliveryID == "" {
				deliveryID = c.GetHeader("X-Hook-ID")
			}
		}

		if deliveryID == "" {
			h.logger.Warn("Missing delivery ID header", "path", c.Request.URL.Path, "method", c.Request.Method)
			JSON(c, http.StatusBadRequest, NewErrorResponse("MISSING_DELIVERY_ID", "Delivery ID header is required"))
			c.Abort()
			return
		}

		// Store delivery ID in context for handler
		c.Set("delivery_id", deliveryID)
		c.Next()
	}
}

// HandleWebhook handles POST /webhook
func (h *WebhookHandler) HandleWebhook(c *gin.Context) {
	// Get delivery ID from context (set by middleware)
	deliveryID, exists := c.Get("delivery_id")
	if !exists {
		BadRequest(c, "Missing delivery ID")
		return
	}

	// Get source from headers or use a default
	source := c.GetHeader("X-Source")
	if source == "" {
		source = c.GetHeader("User-Agent")
		if source == "" {
			source = "unknown"
		}
	}

	// Read the payload
	payload, err := io.ReadAll(c.Request.Body)
	if err != nil {
		h.logger.Error("Failed to read request body", "error", err)
		BadRequest(c, "Failed to read request body")
		return
	}

	if len(payload) == 0 {
		BadRequest(c, "Empty payload")
		return
	}

	// Create command
	cmd := app.HandleWebhookCommand{
		DeliveryID: deliveryID.(string),
		Source:     source,
		Payload:    payload,
	}

	// Handle the webhook
	err = h.webhookHandler.HandleWebhook(c.Request.Context(), cmd)
	if err != nil {
		statusCode := app.HTTPStatusCode(err)

		switch err {
		case app.ErrDuplicate:
			// Idempotent response for duplicates
			h.logger.Info("Duplicate webhook ignored", "delivery_id", deliveryID, "source", source)
			Success(c, gin.H{"status": "already_processed", "delivery_id": deliveryID})
		case app.ErrInvalidID, app.ErrInvalidWebhookData:
			h.logger.Warn("Invalid webhook data", "delivery_id", deliveryID, "source", source, "error", err)
			JSON(c, statusCode, NewErrorResponse("INVALID_WEBHOOK", err.Error()))
		case app.ErrPublishFailed:
			h.logger.Error("Failed to publish webhook", "delivery_id", deliveryID, "source", source, "error", err)
			JSON(c, statusCode, NewErrorResponse("PUBLISH_FAILED", "Failed to process webhook"))
		default:
			h.logger.Error("Webhook processing failed", "delivery_id", deliveryID, "source", source, "error", err)
			JSON(c, statusCode, NewErrorResponse("PROCESSING_FAILED", "Internal server error"))
		}
		return
	}

	// Success response
	h.logger.Info("Webhook processed successfully", "delivery_id", deliveryID, "source", source)
	Success(c, gin.H{
		"status":      "processed",
		"delivery_id": deliveryID,
		"source":      source,
	})
}