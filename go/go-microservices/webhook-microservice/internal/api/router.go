package api

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/Matrics-io/the-manhattan-project-huly/go/shared/tracing"
	"github.com/Matrics-io/the-manhattan-project-huly/go/shared/metrics"
	"webhook_microservice/internal/container"
)

// RegisterRoutes registers all API routes
func RegisterRoutes(router *gin.Engine, container *container.Container) {
	// Initialize shared metrics
	metrics.Initialize()

	// Add shared middleware
	router.Use(gin.Recovery())
	router.Use(gin.Logger())
	router.Use(tracing.Middleware("webhook-microservice"))
	router.Use(metrics.PrometheusMiddleware())

	// Add request logging middleware
	router.Use(func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		method := c.Request.Method

		// Process request
		c.Next()

		// Log request details
		latency := time.Since(start)
		status := c.Writer.Status()
		clientIP := c.ClientIP()

		container.Logger().Info("Request processed",
			"method", method,
			"path", path,
			"status", status,
			"latency", latency,
			"client_ip", clientIP,
		)
	})

	// Register health endpoints with container health check
	router.GET("/healthz", func(c *gin.Context) {
		Success(c, gin.H{"status": "ok"})
	})

	router.GET("/readyz", func(c *gin.Context) {
		if err := container.HealthCheck(c.Request.Context()); err != nil {
			JSON(c, http.StatusServiceUnavailable, NewErrorResponse("SERVICE_UNAVAILABLE", "Service not ready"))
			return
		}
		Success(c, gin.H{"status": "ready"})
	})

	// Register metrics endpoint
	metrics.RegisterMetricsEndpoint(router)

	// Register webhook routes
	webhookHandler := NewWebhookHandler(container)
	webhookHandler.RegisterRoutes(router)
}

// RegisterTestRoutes registers routes for testing without middleware
func RegisterTestRoutes(router *gin.Engine, container *container.Container) {
	// Initialize shared metrics
	metrics.Initialize()

	// Add minimal middleware for tests
	router.Use(gin.Recovery())

	// Register health endpoints
	router.GET("/healthz", func(c *gin.Context) {
		Success(c, gin.H{"status": "ok"})
	})

	router.GET("/readyz", func(c *gin.Context) {
		Success(c, gin.H{"status": "ready"})
	})

	// Register metrics endpoint
	metrics.RegisterMetricsEndpoint(router)

	// Register webhook routes without middleware
	webhookHandler := NewWebhookHandler(container)
	router.POST("/webhook", webhookHandler.HandleWebhook)
}