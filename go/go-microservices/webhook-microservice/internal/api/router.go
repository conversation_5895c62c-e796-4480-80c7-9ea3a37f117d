package api

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"webhook_microservice/internal/container"
	"webhook_microservice/internal/logging"
	"webhook_microservice/internal/observability"
)

// RegisterRoutes registers all API routes
func RegisterRoutes(router *gin.Engine, container *container.Container) {
	// Add global middleware
	router.Use(
		RecoveryMiddleware(),
		CORSMiddleware(),
		RateLimitMiddleware(100, 200), // 100 requests per second, burst of 200
		TimeoutMiddleware(30 * time.Second),
		observability.PrometheusMiddleware(),
	)

	// Add request logging middleware
	router.Use(func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		method := c.Request.Method

		// Process request
		c.Next()

		// Log request details
		latency := time.Since(start)
		status := c.Writer.Status()
		clientIP := c.ClientIP()

		logger := logging.GetLogger()
		logger.Info("Request processed",
			zap.String("method", method),
			zap.String("path", path),
			zap.Int("status", status),
			zap.Duration("latency", latency),
			zap.String("client_ip", clientIP),
		)
	})

	// Register health endpoints with container health check
	router.GET("/healthz", func(c *gin.Context) {
		Success(c, gin.H{"status": "ok"})
	})

	router.GET("/readyz", func(c *gin.Context) {
		if err := container.HealthCheck(c.Request.Context()); err != nil {
			JSON(c, http.StatusServiceUnavailable, NewErrorResponse("SERVICE_UNAVAILABLE", "Service not ready"))
			return
		}
		Success(c, gin.H{"status": "ready"})
	})

	// Register webhook routes
	webhookHandler := NewWebhookHandler(container)
	webhookHandler.RegisterRoutes(router)
} 