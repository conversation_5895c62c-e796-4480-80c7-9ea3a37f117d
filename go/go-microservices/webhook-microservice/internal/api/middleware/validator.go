package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// ValidatorConfig holds validation configuration
type ValidatorConfig struct {
	RequiredHeaders []string
}

// ValidatorMiddleware validates required headers and basic webhook structure
func ValidatorMiddleware(config ValidatorConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check required headers
		for _, header := range config.RequiredHeaders {
			value := c.GetHeader(header)
			if value == "" {
				// For delivery ID, try alternative header names
				if strings.Contains(strings.ToLower(header), "delivery") {
					if altValue := c.GetHeader("X-GitHub-Delivery"); altValue != "" {
						c.Set("delivery_id", altValue)
						continue
					}
					if altValue := c.GetHeader("X-Hook-ID"); altValue != "" {
						c.Set("delivery_id", altValue)
						continue
					}
				}
				
				c.<PERSON><PERSON>(http.StatusBadRequest, gin.H{
					"error": "Missing required header: " + header,
					"code":  "MISSING_HEADER",
				})
				c.Abort()
				return
			}
			
			// Store delivery ID for later use
			if strings.Contains(strings.ToLower(header), "delivery") {
				c.Set("delivery_id", value)
			}
		}
		
		// Validate Content-Type for POST requests
		if c.Request.Method == "POST" {
			contentType := c.GetHeader("Content-Type")
			if contentType == "" {
				c.JSON(http.StatusBadRequest, gin.H{
					"error": "Content-Type header is required",
					"code":  "MISSING_CONTENT_TYPE",
				})
				c.Abort()
				return
			}
			
			// Accept common webhook content types
			validContentTypes := []string{
				"application/json",
				"application/x-www-form-urlencoded",
				"text/plain",
			}
			
			isValidContentType := false
			for _, validType := range validContentTypes {
				if strings.Contains(strings.ToLower(contentType), validType) {
					isValidContentType = true
					break
				}
			}
			
			if !isValidContentType {
				c.JSON(http.StatusUnsupportedMediaType, gin.H{
					"error": "Unsupported Content-Type: " + contentType,
					"code":  "UNSUPPORTED_CONTENT_TYPE",
				})
				c.Abort()
				return
			}
		}
		
		c.Next()
	}
}

// SignatureValidatorMiddleware validates webhook signatures (placeholder for future implementation)
func SignatureValidatorMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: Implement signature validation based on webhook source
		// For now, just log the signature headers
		signature := c.GetHeader("X-Signature")
		if signature == "" {
			signature = c.GetHeader("X-Hub-Signature")
		}
		if signature == "" {
			signature = c.GetHeader("X-Hub-Signature-256")
		}
		
		// Store signature for potential validation in handler
		if signature != "" {
			c.Set("webhook_signature", signature)
		}
		
		c.Next()
	}
}
