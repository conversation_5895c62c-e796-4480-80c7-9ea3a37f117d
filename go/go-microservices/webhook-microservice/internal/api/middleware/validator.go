package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// ValidatorConfig holds validation configuration
type ValidatorConfig struct {
	RequiredHeaders []string
}

// WebhookSource represents the source of a webhook
type WebhookSource string

const (
	SourceGitHub    WebhookSource = "github"
	SourceJira      WebhookSource = "jira"
	SourceCrossChex WebhookSource = "crosschex"
	SourceGeneric   WebhookSource = "generic"
)

// detectWebhookSource detects the webhook source based on headers
func detectWebhookSource(c *gin.Context) WebhookSource {
	// GitHub webhooks
	if c.GetHeader("X-GitHub-Event") != "" || c.<PERSON>Header("X-GitHub-Delivery") != "" {
		return SourceGitHub
	}

	// Jira/Atlassian webhooks
	if c.GetHeader("X-Atlassian-Webhook-Identifier") != "" || c.<PERSON>eader("X-Event-Key") != "" {
		return SourceJira
	}

	// CrossChex webhooks (based on User-Agent or custom headers)
	userAgent := c.<PERSON>("User-Agent")
	if strings.Contains(strings.ToLower(userAgent), "crosschex") {
		return SourceCrossChex
	}

	return SourceGeneric
}

// ValidatorMiddleware validates required headers and basic webhook structure
func ValidatorMiddleware(config ValidatorConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Detect webhook source
		source := detectWebhookSource(c)
		c.Set("webhook_source", source)

		// Extract and store webhook metadata based on source
		extractWebhookMetadata(c, source)

		// Check required headers with source-specific fallbacks
		deliveryID := getDeliveryID(c, source)
		if deliveryID == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Missing delivery ID header",
				"code":  "MISSING_DELIVERY_ID",
				"supported_headers": getSupportedDeliveryHeaders(source),
			})
			c.Abort()
			return
		}
		c.Set("delivery_id", deliveryID)

		// Validate other required headers
		for _, header := range config.RequiredHeaders {
			if strings.Contains(strings.ToLower(header), "delivery") {
				continue // Already handled above
			}

			value := c.GetHeader(header)
			if value == "" {
				c.JSON(http.StatusBadRequest, gin.H{
					"error": "Missing required header: " + header,
					"code":  "MISSING_HEADER",
				})
				c.Abort()
				return
			}
		}
		
		// Validate Content-Type for POST requests
		if c.Request.Method == "POST" {
			contentType := c.GetHeader("Content-Type")
			if contentType == "" {
				c.JSON(http.StatusBadRequest, gin.H{
					"error": "Content-Type header is required",
					"code":  "MISSING_CONTENT_TYPE",
				})
				c.Abort()
				return
			}
			
			// Accept common webhook content types
			validContentTypes := []string{
				"application/json",
				"application/x-www-form-urlencoded",
				"text/plain",
			}
			
			isValidContentType := false
			for _, validType := range validContentTypes {
				if strings.Contains(strings.ToLower(contentType), validType) {
					isValidContentType = true
					break
				}
			}
			
			if !isValidContentType {
				c.JSON(http.StatusUnsupportedMediaType, gin.H{
					"error": "Unsupported Content-Type: " + contentType,
					"code":  "UNSUPPORTED_CONTENT_TYPE",
				})
				c.Abort()
				return
			}
		}
		
		c.Next()
	}
}

// SignatureValidatorMiddleware validates webhook signatures (placeholder for future implementation)
func SignatureValidatorMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: Implement signature validation based on webhook source
		// For now, just log the signature headers
		signature := c.GetHeader("X-Signature")
		if signature == "" {
			signature = c.GetHeader("X-Hub-Signature")
		}
		if signature == "" {
			signature = c.GetHeader("X-Hub-Signature-256")
		}
		
		// Store signature for potential validation in handler
		if signature != "" {
			c.Set("webhook_signature", signature)
		}
		
		c.Next()
	}
}

// getDeliveryID extracts delivery ID based on webhook source
func getDeliveryID(c *gin.Context, source WebhookSource) string {
	switch source {
	case SourceGitHub:
		if id := c.GetHeader("X-GitHub-Delivery"); id != "" {
			return id
		}
		if id := c.GetHeader("X-GitHub-Hook-ID"); id != "" {
			return id
		}
	case SourceJira:
		if id := c.GetHeader("X-Atlassian-Webhook-Identifier"); id != "" {
			return id
		}
	case SourceCrossChex:
		// CrossChex might use custom headers
		if id := c.GetHeader("X-CrossChex-Delivery-ID"); id != "" {
			return id
		}
	}

	// Generic fallbacks
	if id := c.GetHeader("X-Delivery-ID"); id != "" {
		return id
	}
	if id := c.GetHeader("X-Hook-ID"); id != "" {
		return id
	}

	return ""
}

// getSupportedDeliveryHeaders returns supported delivery headers for a source
func getSupportedDeliveryHeaders(source WebhookSource) []string {
	switch source {
	case SourceGitHub:
		return []string{"X-GitHub-Delivery", "X-GitHub-Hook-ID"}
	case SourceJira:
		return []string{"X-Atlassian-Webhook-Identifier"}
	case SourceCrossChex:
		return []string{"X-CrossChex-Delivery-ID", "X-Delivery-ID"}
	default:
		return []string{"X-Delivery-ID", "X-Hook-ID"}
	}
}

// extractWebhookMetadata extracts and stores webhook metadata
func extractWebhookMetadata(c *gin.Context, source WebhookSource) {
	switch source {
	case SourceGitHub:
		if event := c.GetHeader("X-GitHub-Event"); event != "" {
			c.Set("webhook_event", event)
		}
		if hookID := c.GetHeader("X-GitHub-Hook-ID"); hookID != "" {
			c.Set("webhook_hook_id", hookID)
		}
	case SourceJira:
		if event := c.GetHeader("X-Event-Key"); event != "" {
			c.Set("webhook_event", event)
		}
	case SourceCrossChex:
		// Extract CrossChex specific metadata
		if event := c.GetHeader("X-CrossChex-Event"); event != "" {
			c.Set("webhook_event", event)
		}
	}

	// Store user agent for all sources
	if userAgent := c.GetHeader("User-Agent"); userAgent != "" {
		c.Set("webhook_user_agent", userAgent)
	}
}
