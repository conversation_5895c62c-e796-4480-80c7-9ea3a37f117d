package middleware

import (
	"net"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"golang.org/x/time/rate"
)

// WAFConfig holds WAF configuration
type WAFConfig struct {
	AllowList    []string
	RateLimitRPM int
}

// WAFMiddleware implements IP allow-list and rate limiting
func WAFMiddleware(config WAFConfig) gin.HandlerFunc {
	// Create rate limiter (requests per minute converted to per second)
	limiter := rate.NewLimiter(rate.Limit(config.RateLimitRPM)/60, config.RateLimitRPM)
	
	return func(c *gin.Context) {
		clientIP := c.ClientIP()
		
		// Check IP allow-list
		if !isIPAllowed(clientIP, config.AllowList) {
			c.JSO<PERSON>(http.StatusForbidden, gin.H{
				"error": "IP not allowed",
				"code":  "IP_FORBIDDEN",
			})
			c.Abort()
			return
		}
		
		// Check rate limit
		if !limiter.Allow() {
			c.JSO<PERSON>(http.StatusTooManyRequests, gin.H{
				"error": "Rate limit exceeded",
				"code":  "RATE_LIMIT_EXCEEDED",
			})
			c.Abort()
			return
		}
		
		c.Next()
	}
}

// isIPAllowed checks if an IP is in the allow list
func isIPAllowed(clientIP string, allowList []string) bool {
	if len(allowList) == 0 {
		return true // No restrictions if allow list is empty
	}
	
	for _, allowed := range allowList {
		// Handle CIDR notation
		if strings.Contains(allowed, "/") {
			_, network, err := net.ParseCIDR(allowed)
			if err != nil {
				continue
			}
			ip := net.ParseIP(clientIP)
			if ip != nil && network.Contains(ip) {
				return true
			}
		} else {
			// Handle exact IP match
			if clientIP == allowed {
				return true
			}
		}
	}
	
	return false
}
