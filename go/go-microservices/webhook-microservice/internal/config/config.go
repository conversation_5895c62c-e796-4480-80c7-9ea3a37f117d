package config

import (
	"fmt"
	"log/slog"
	"os"
	"strings"

	"github.com/spf13/viper"
)

// Config represents the webhook microservice configuration
type Config struct {
	Port           int      `yaml:"port" mapstructure:"port"`
	PubSubName     string   `yaml:"pubsub_name" mapstructure:"pubsub_name"`
	TopicName      string   `yaml:"topic_name" mapstructure:"topic_name"`
	StateStoreName string   `yaml:"state_store_name" mapstructure:"state_store_name"`
	RateLimitRPM   int      `yaml:"rate_limit_rpm" mapstructure:"rate_limit_rpm"`
	WAFAllowList   []string `yaml:"waf_allow_list" mapstructure:"waf_allow_list"`
}

// LoadConfig loads the webhook microservice configuration
func LoadConfig() (*Config, error) {
	v := viper.New()

	// Set config file
	configPath := os.Getenv("CONFIG_PATH")
	if configPath == "" {
		configPath = "configs/config.yaml"
	}

	v.SetConfigFile(configPath)
	v.SetConfigType("yaml")

	// Configure environment variable handling
	v.SetEnvPrefix("WEBHOOK")
	v.AutomaticEnv()
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// Set defaults
	v.SetDefault("port", 8080)
	v.SetDefault("pubsub_name", "redis-pubsub")
	v.SetDefault("topic_name", "webhook-events")
	v.SetDefault("state_store_name", "statestore")
	v.SetDefault("rate_limit_rpm", 1000)
	v.SetDefault("waf_allow_list", []string{"0.0.0.0/0"})

	// Try to read config file, but don't fail if it doesn't exist
	if err := v.ReadInConfig(); err != nil {
		// If config file doesn't exist, use environment variables and defaults
		slog.Info("Config file not found, using environment variables and defaults", "error", err)
	}

	// Environment variables are automatically handled by Viper with WEBHOOK_ prefix
	// Manual override for WAF allow list to handle comma-separated values
	if allowList := os.Getenv("WEBHOOK_WAF_ALLOW_LIST"); allowList != "" {
		v.Set("waf_allow_list", strings.Split(allowList, ","))
	}

	var config Config
	if err := v.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &config, nil
}

