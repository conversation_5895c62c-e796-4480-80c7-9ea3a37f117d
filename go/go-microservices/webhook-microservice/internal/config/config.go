package config

import (
	"fmt"
	"log/slog"
	"os"
	"strconv"
	"strings"

	"github.com/spf13/viper"
)

// Config represents the webhook microservice configuration
type Config struct {
	Port           int      `yaml:"port"`
	PubSubName     string   `yaml:"pubsub_name"`
	TopicName      string   `yaml:"topic_name"`
	StateStoreName string   `yaml:"state_store_name"`
	RateLimitRPM   int      `yaml:"rate_limit_rpm"`
	WAFAllowList   []string `yaml:"waf_allow_list"`
}

// LoadConfig loads the webhook microservice configuration
func LoadConfig() (*Config, error) {
	v := viper.New()

	// Set default values
	v.SetDefault("port", 8080)
	v.SetDefault("pubsub_name", "redis-pubsub")
	v.SetDefault("topic_name", "webhook-events")
	v.SetDefault("state_store_name", "statestore")
	v.SetDefault("rate_limit_rpm", 1000)
	v.SetDefault("waf_allow_list", []string{"0.0.0.0/0"})

	// Set config file
	configPath := os.Getenv("CONFIG_PATH")
	if configPath == "" {
		configPath = "configs/config.yaml"
	}

	v.SetConfigFile(configPath)
	v.SetConfigType("yaml")
	v.AutomaticEnv()

	// Try to read config file, but don't fail if it doesn't exist
	if err := v.ReadInConfig(); err != nil {
		// If config file doesn't exist, use environment variables and defaults
		slog.Info("Config file not found, using environment variables and defaults", "error", err)
	}

	// Override with environment variables
	if port := os.Getenv("WEBHOOK_PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			v.Set("port", p)
		}
	}
	if pubsub := os.Getenv("WEBHOOK_PUBSUB_NAME"); pubsub != "" {
		v.Set("pubsub_name", pubsub)
	}
	if topic := os.Getenv("WEBHOOK_TOPIC_NAME"); topic != "" {
		v.Set("topic_name", topic)
	}
	if stateStore := os.Getenv("WEBHOOK_STATE_STORE"); stateStore != "" {
		v.Set("state_store_name", stateStore)
	}
	if rateLimit := os.Getenv("WEBHOOK_RATE_LIMIT_RPM"); rateLimit != "" {
		if r, err := strconv.Atoi(rateLimit); err == nil {
			v.Set("rate_limit_rpm", r)
		}
	}
	if allowList := os.Getenv("WEBHOOK_WAF_ALLOW_LIST"); allowList != "" {
		v.Set("waf_allow_list", strings.Split(allowList, ","))
	}

	var config Config
	if err := v.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &config, nil
}

