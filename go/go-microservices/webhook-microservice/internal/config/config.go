package config

import (
	"fmt"
	"log/slog"
	"os"
	"strings"

	"github.com/spf13/viper"
)

// Config represents the webhook microservice configuration
type Config struct {
	Server        ServerConfig        `yaml:"server" mapstructure:"server"`
	Environment   string              `yaml:"environment" mapstructure:"environment"`
	Logging       LoggingConfig       `yaml:"logging" mapstructure:"logging"`
	Observability ObservabilityConfig `yaml:"observability" mapstructure:"observability"`
	Dapr          DaprConfig          `yaml:"dapr" mapstructure:"dapr"`
	Webhook       WebhookConfig       `yaml:"webhook" mapstructure:"webhook"`
	Redis         RedisConfig         `yaml:"redis" mapstructure:"redis"`
}

// ServerConfig holds server configuration
type ServerConfig struct {
	Port int    `yaml:"port" mapstructure:"port"`
	Host string `yaml:"host" mapstructure:"host"`
}

// LoggingConfig holds logging configuration
type LoggingConfig struct {
	Level string `yaml:"level" mapstructure:"level"`
}

// ObservabilityConfig holds observability configuration
type ObservabilityConfig struct {
	Prometheus PrometheusConfig `yaml:"prometheus" mapstructure:"prometheus"`
	Tracing    TracingConfig    `yaml:"tracing" mapstructure:"tracing"`
}

// PrometheusConfig holds Prometheus configuration
type PrometheusConfig struct {
	Enabled bool   `yaml:"enabled" mapstructure:"enabled"`
	Path    string `yaml:"path" mapstructure:"path"`
}

// TracingConfig holds tracing configuration
type TracingConfig struct {
	Enabled bool   `yaml:"enabled" mapstructure:"enabled"`
	Jaeger  string `yaml:"jaeger" mapstructure:"jaeger"`
}

// DaprConfig holds Dapr configuration
type DaprConfig struct {
	Enabled     bool   `yaml:"enabled" mapstructure:"enabled"`
	AppID       string `yaml:"app_id" mapstructure:"app_id"`
	HTTPPort    int    `yaml:"http_port" mapstructure:"http_port"`
	GRPCPort    int    `yaml:"grpc_port" mapstructure:"grpc_port"`
	StateStore  string `yaml:"state_store" mapstructure:"state_store"`
	PubSubName  string `yaml:"pubsub_name" mapstructure:"pubsub_name"`
	SecretStore string `yaml:"secret_store" mapstructure:"secret_store"`
}

// WebhookConfig holds webhook-specific configuration
type WebhookConfig struct {
	TopicName       string   `yaml:"topic_name" mapstructure:"topic_name"`
	RateLimitRPM    int      `yaml:"rate_limit_rpm" mapstructure:"rate_limit_rpm"`
	WAFAllowList    []string `yaml:"waf_allow_list" mapstructure:"waf_allow_list"`
	RequiredHeaders []string `yaml:"required_headers" mapstructure:"required_headers"`
}

// RedisConfig holds Redis configuration
type RedisConfig struct {
	Addr         string `yaml:"addr" mapstructure:"addr"`
	Password     string `yaml:"password" mapstructure:"password"`
	DB           int    `yaml:"db" mapstructure:"db"`
	PoolSize     int    `yaml:"pool_size" mapstructure:"pool_size"`
	MinIdleConns int    `yaml:"min_idle_conns" mapstructure:"min_idle_conns"`
	MaxRetries   int    `yaml:"max_retries" mapstructure:"max_retries"`
	KeyPrefix    string `yaml:"key_prefix" mapstructure:"key_prefix"`
	DefaultTTL   int    `yaml:"default_ttl" mapstructure:"default_ttl"`
}

// LoadConfig loads the webhook microservice configuration
func LoadConfig() (*Config, error) {
	v := viper.New()

	// Set config file
	configPath := os.Getenv("CONFIG_PATH")
	if configPath == "" {
		configPath = "configs/config.yaml"
	}

	v.SetConfigFile(configPath)
	v.SetConfigType("yaml")

	// Configure environment variable handling
	v.SetEnvPrefix("WEBHOOK")
	v.AutomaticEnv()
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// Set defaults
	setDefaults(v)

	// Try to read config file, but don't fail if it doesn't exist
	if err := v.ReadInConfig(); err != nil {
		// If config file doesn't exist, use environment variables and defaults
		slog.Info("Config file not found, using environment variables and defaults", "error", err)
	}

	// Handle special environment variable overrides
	handleEnvOverrides(v)

	var config Config
	if err := v.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &config, nil
}

// setDefaults sets default configuration values
func setDefaults(v *viper.Viper) {
	// Server defaults
	v.SetDefault("server.port", 8080)
	v.SetDefault("server.host", "0.0.0.0")

	// Environment
	v.SetDefault("environment", "development")

	// Logging defaults
	v.SetDefault("logging.level", "info")

	// Observability defaults
	v.SetDefault("observability.prometheus.enabled", true)
	v.SetDefault("observability.prometheus.path", "/metrics")
	v.SetDefault("observability.tracing.enabled", false)
	v.SetDefault("observability.tracing.jaeger", "http://localhost:14268/api/traces")

	// Dapr defaults
	v.SetDefault("dapr.enabled", true)
	v.SetDefault("dapr.app_id", "webhook-microservice")
	v.SetDefault("dapr.http_port", 3500)
	v.SetDefault("dapr.grpc_port", 50001)
	v.SetDefault("dapr.state_store", "statestore")
	v.SetDefault("dapr.pubsub_name", "redis-pubsub")
	v.SetDefault("dapr.secret_store", "config-secret-store")

	// Webhook defaults
	v.SetDefault("webhook.topic_name", "webhook-events")
	v.SetDefault("webhook.rate_limit_rpm", 1000)
	v.SetDefault("webhook.waf_allow_list", []string{"0.0.0.0/0"})
	v.SetDefault("webhook.required_headers", []string{"X-Delivery-ID"})

	// Redis defaults
	v.SetDefault("redis.addr", "localhost:6379")
	v.SetDefault("redis.password", "")
	v.SetDefault("redis.db", 0)
	v.SetDefault("redis.pool_size", 10)
	v.SetDefault("redis.min_idle_conns", 5)
	v.SetDefault("redis.max_retries", 3)
	v.SetDefault("redis.key_prefix", "webhook:")
	v.SetDefault("redis.default_ttl", 86400)
}

// handleEnvOverrides handles special environment variable overrides
func handleEnvOverrides(v *viper.Viper) {
	// Handle comma-separated lists
	if allowList := os.Getenv("WEBHOOK_WEBHOOK_WAF_ALLOW_LIST"); allowList != "" {
		v.Set("webhook.waf_allow_list", strings.Split(allowList, ","))
	}

	if headers := os.Getenv("WEBHOOK_WEBHOOK_REQUIRED_HEADERS"); headers != "" {
		v.Set("webhook.required_headers", strings.Split(headers, ","))
	}
}

// Convenience methods for backward compatibility and easy access

// Port returns the server port
func (c *Config) Port() int {
	return c.Server.Port
}

// Host returns the server host
func (c *Config) Host() string {
	return c.Server.Host
}

// PubSubName returns the Dapr pub/sub component name
func (c *Config) PubSubName() string {
	return c.Dapr.PubSubName
}

// TopicName returns the webhook topic name
func (c *Config) TopicName() string {
	return c.Webhook.TopicName
}

// StateStoreName returns the Dapr state store component name
func (c *Config) StateStoreName() string {
	return c.Dapr.StateStore
}

// RateLimitRPM returns the rate limit in requests per minute
func (c *Config) RateLimitRPM() int {
	return c.Webhook.RateLimitRPM
}

// WAFAllowList returns the WAF allow list
func (c *Config) WAFAllowList() []string {
	return c.Webhook.WAFAllowList
}

// RequiredHeaders returns the required webhook headers
func (c *Config) RequiredHeaders() []string {
	return c.Webhook.RequiredHeaders
}

