package config

import (
	"fmt"
	"os"
	"time"

	"github.com/spf13/viper"
)

// Config represents the application configuration
type Config struct {
	Server struct {
		Port int    `yaml:"port"`
		Host string `yaml:"host"`
	} `yaml:"server"`

	Environment string `yaml:"environment"`

	Logging struct {
		Level string `yaml:"level"`
	} `yaml:"logging"`

	Observability struct {
		Prometheus struct {
			Enabled bool   `yaml:"enabled"`
			Path    string `yaml:"path"`
		} `yaml:"prometheus"`

		Tracing struct {
			Enabled bool   `yaml:"enabled"`
			Jaeger  string `yaml:"jaeger"`
		} `yaml:"tracing"`
	} `yaml:"observability"`

	ClickHouse struct {
		DSN string `yaml:"dsn"`
	} `yaml:"clickhouse"`

	MongoDB struct {
		URI string `yaml:"uri"`
	} `yaml:"mongodb"`

	Redis struct {
		Addr          string        `yaml:"addr"`
		Password      string        `yaml:"password"`
		DB            int           `yaml:"db"`
		PoolSize      int           `yaml:"pool_size"`
		MinIdleConns  int           `yaml:"min_idle_conns"`
		MaxRetries    int           `yaml:"max_retries"`
		KeyPrefix     string        `yaml:"key_prefix"`
		DefaultTTL    time.Duration `yaml:"default_ttl"`
	} `yaml:"redis"`

	// New Dapr configuration section
	Dapr struct {
		Enabled     bool   `yaml:"enabled"`
		AppID       string `yaml:"app_id"`
		HTTPPort    int    `yaml:"http_port"`
		GRPCPort    int    `yaml:"grpc_port"`
		StateStore  string `yaml:"state_store"`
		PubSubName  string `yaml:"pubsub_name"`
		SecretStore string `yaml:"secret_store"`
	} `yaml:"dapr"`

	// Secrets section that can be used by Dapr or directly
	Secrets struct {
		Database struct {
			ConnectionString string `yaml:"connection_string"`
			Username         string `yaml:"username"`
			Password         string `yaml:"password"`
		} `yaml:"database"`
		
		API struct {
			Key    string `yaml:"key"`
			Secret string `yaml:"secret"`
		} `yaml:"api"`
		
		External struct {
			ServiceAKey    string `yaml:"service_a_key"`
			ServiceBSecret string `yaml:"service_b_secret"`
		} `yaml:"external"`
	} `yaml:"secrets"`
}

// Load loads the configuration from a YAML file
func Load(path string) (*Config, error) {
	v := viper.New()

	// Set default values
	v.SetDefault("server.host", "0.0.0.0")
	v.SetDefault("server.port", 8080)
	v.SetDefault("logging.level", "info")
	v.SetDefault("observability.prometheus.enabled", true)
	v.SetDefault("observability.prometheus.path", "/metrics")
	v.SetDefault("redis.pool_size", 10)
	v.SetDefault("redis.min_idle_conns", 5)
	v.SetDefault("redis.max_retries", 3)
	v.SetDefault("redis.key_prefix", "template:")
	v.SetDefault("redis.default_ttl", 3600)
	
	// Dapr defaults
	v.SetDefault("dapr.enabled", true)
	v.SetDefault("dapr.app_id", "template-microservice")
	v.SetDefault("dapr.http_port", 3500)
	v.SetDefault("dapr.grpc_port", 50001)
	v.SetDefault("dapr.state_store", "statestore")
	v.SetDefault("dapr.pubsub_name", "redis-pubsub")
	v.SetDefault("dapr.secret_store", "config-secret-store")

	// Set config file
	if path == "" {
		path = os.Getenv("CONFIG_PATH")
	}
	if path == "" {
		path = "configs/config.yaml"
	}

	v.SetConfigFile(path)
	v.SetConfigType("yaml")
	v.AutomaticEnv()

	if err := v.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := v.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// Set defaults if not provided
	if config.Server.Port == 0 {
		config.Server.Port = 8080
	}
	if config.Server.Host == "" {
		config.Server.Host = "localhost"
	}
	if config.Environment == "" {
		config.Environment = "development"
	}
	if config.Logging.Level == "" {
		config.Logging.Level = "info"
	}

	return &config, nil
}

// GetSecret returns a secret value by path (for Dapr secrets compatibility)
func (c *Config) GetSecret(secretPath string) (map[string]string, error) {
	secrets := make(map[string]string)
	
	switch secretPath {
	case "database":
		secrets["connection_string"] = c.Secrets.Database.ConnectionString
		secrets["username"] = c.Secrets.Database.Username
		secrets["password"] = c.Secrets.Database.Password
	case "api":
		secrets["key"] = c.Secrets.API.Key
		secrets["secret"] = c.Secrets.API.Secret
	case "external":
		secrets["service_a_key"] = c.Secrets.External.ServiceAKey
		secrets["service_b_secret"] = c.Secrets.External.ServiceBSecret
	default:
		return nil, fmt.Errorf("secret path '%s' not found", secretPath)
	}
	
	return secrets, nil
}

// LoadFromEnv loads configuration from environment variables
func LoadFromEnv() (*Config, error) {
	config := &Config{}

	// Set defaults
	config.Server.Port = 8080
	config.Server.Host = "localhost"
	config.Environment = "development"
	config.Logging.Level = "info"
	config.Observability.Prometheus.Enabled = true
	config.Observability.Prometheus.Path = "/metrics"
	config.Observability.Tracing.Enabled = false
	config.Dapr.Enabled = true
	config.Dapr.AppID = "template-microservice"
	config.Dapr.HTTPPort = 3500
	config.Dapr.GRPCPort = 50001

	// Override with environment variables if they exist
	if port := os.Getenv("SERVER_PORT"); port != "" {
		fmt.Sscanf(port, "%d", &config.Server.Port)
	}
	if host := os.Getenv("SERVER_HOST"); host != "" {
		config.Server.Host = host
	}
	if env := os.Getenv("ENV"); env != "" {
		config.Environment = env
	}
	if level := os.Getenv("LOG_LEVEL"); level != "" {
		config.Logging.Level = level
	}

	return config, nil
} 

// getEnvOrDefault returns environment variable value or default if not set
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// LoadFromEnvWithFallback tries environment first, then falls back to file-based config
func LoadFromEnvWithFallback(configPath string) (*Config, error) {
	// Try environment first
	if os.Getenv("USE_ENV_CONFIG") == "true" {
		return LoadFromEnv()
	}
	
	// Fall back to file-based config
	return Load(configPath)
} 