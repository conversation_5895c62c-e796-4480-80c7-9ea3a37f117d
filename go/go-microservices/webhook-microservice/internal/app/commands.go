package app

import (
	"template_microservice/internal/domain"
)

// CreateTemplateCommand represents the command to create a new template
type CreateTemplateCommand struct {
	Name    string
	Content string
}

// CreateTemplateHandler handles the creation of new templates
type CreateTemplateHandler struct {
	repo TemplateRepository
}

// NewCreateTemplateHandler creates a new CreateTemplateHandler
func NewCreateTemplateHandler(repo TemplateRepository) *CreateTemplateHandler {
	return &CreateTemplateHandler{
		repo: repo,
	}
}

// <PERSON>le processes the CreateTemplateCommand
func (h *CreateTemplateHandler) Handle(cmd CreateTemplateCommand) (*domain.Template, error) {
	if cmd.Name == "" || cmd.Content == "" {
		return nil, ErrInvalidTemplate
	}

	template := domain.NewTemplate(cmd.Name, cmd.Content)
	if err := h.repo.Create(template); err != nil {
		return nil, err
	}

	return template, nil
} 