package app

import (
	"context"
	"log/slog"
	"template_microservice/internal/domain"
)

// TemplateService provides template-related business operations
type TemplateService struct {
	createHandler  *CreateTemplateHandler
	getHandler     *GetTemplateHandler
	eventPublisher EventPublisher
	logger         *slog.Logger
}

// NewTemplateService creates a new TemplateService instance
func NewTemplateService(repo TemplateRepository, mongoRepo TemplateRepository, eventPublisher EventPublisher, logger *slog.Logger) *TemplateService {
	return &TemplateService{
		createHandler:  NewCreateTemplateHandler(repo),
		getHandler:     NewGetTemplateHandler(repo),
		eventPublisher: eventPublisher,
		logger:         logger,
	}
}

// CreateTemplate handles template creation with logging and event publishing
func (s *TemplateService) CreateTemplate(ctx context.Context, cmd CreateTemplateCommand) (*domain.Template, error) {
	s.logger.Info("Creating template", "name", cmd.Name)
	
	template, err := s.createHandler.Handle(cmd)
	if err != nil {
		s.logger.Error("Failed to create template", "error", err, "name", cmd.Name)
		return nil, err
	}

	// Publish template created event if event publisher is available
	if s.eventPublisher != nil {
		if err := s.eventPublisher.PublishTemplateCreated(ctx, template); err != nil {
			s.logger.Error("Failed to publish template created event", "error", err, "templateId", template.ID)
			// Don't fail the operation, just log the error
		}
	}

	s.logger.Info("Template created successfully", "templateId", template.ID, "name", template.Name)
	return template, nil
}

// GetTemplate handles template retrieval with logging
func (s *TemplateService) GetTemplate(query GetTemplateQuery) (*domain.Template, error) {
	s.logger.Info("Getting template", "id", query.ID)
	return s.getHandler.Handle(query)
} 