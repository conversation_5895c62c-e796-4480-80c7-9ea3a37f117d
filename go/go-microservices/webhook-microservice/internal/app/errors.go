package app

import "errors"

var (
	// ErrTemplateNotFound is returned when a template cannot be found
	ErrTemplateNotFound = errors.New("template not found")
	
	// ErrInvalidTemplate is returned when template data is invalid
	ErrInvalidTemplate = errors.New("invalid template data")
	
	// ErrTemplateAlreadyExists is returned when a template already exists
	ErrTemplateAlreadyExists = errors.New("template already exists")
) 