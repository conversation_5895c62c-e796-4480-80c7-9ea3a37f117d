package app

import (
	"errors"
	"net/http"
)

var (
	// ErrInvalidID is returned when delivery ID is invalid → 400 Bad Request
	ErrInvalidID = errors.New("invalid delivery ID")

	// ErrDuplicate is returned when webhook is duplicate → 200 OK (idempotent)
	ErrDuplicate = errors.New("duplicate webhook event")

	// ErrPublishFailed is returned when publishing fails → 502 Bad Gateway
	ErrPublishFailed = errors.New("failed to publish webhook event")

	// ErrInvalidWebhookData is returned when webhook data is invalid → 400 Bad Request
	ErrInvalidWebhookData = errors.New("invalid webhook data")

	// ErrDeduplicationFailed is returned when deduplication check fails → 500 Internal Server Error
	ErrDeduplicationFailed = errors.New("deduplication check failed")
)

// HTTPStatusCode maps application errors to HTTP status codes
func HTTPStatusCode(err error) int {
	switch err {
	case ErrInvalidID, ErrInvalidWebhookData:
		return http.StatusBadRequest
	case ErrDuplicate:
		return http.StatusOK // Idempotent - already processed
	case ErrPublishFailed:
		return http.StatusBadGateway
	case ErrDeduplicationFailed:
		return http.StatusInternalServerError
	default:
		return http.StatusInternalServerError
	}
}