package app

import (
	"context"
	"log/slog"

	"webhook_microservice/internal/app"
	"webhook_microservice/internal/domain"
)

// HandleWebhookCommand represents the command to handle an incoming webhook
type HandleWebhookCommand struct {
	DeliveryID string `json:"delivery_id"`
	Source     string `json:"source"`
	Payload    []byte `json:"payload"`
}

// WebhookHandler handles webhook processing
type Webhook<PERSON>andler struct {
	dedupService app.DeduplicationService
	pubService   app.PublishingService
	logger       *slog.Logger
}

// NewWebhookHandler creates a new WebhookHandler
func NewWebhookHandler(
	dedupService app.DeduplicationService,
	pubService app.PublishingService,
	logger *slog.Logger,
) *WebhookHandler {
	return &WebhookHandler{
		dedupService: dedupService,
		pubService:   pubService,
		logger:       logger,
	}
}

// HandleWebhook processes the HandleWebhookCommand
func (h *WebhookHandler) HandleWebhook(ctx context.Context, cmd HandleWebhookCommand) error {
	// Create delivery ID with validation
	id, err := domain.NewDeliveryID(cmd.DeliveryID)
	if err != nil {
		h.logger.Warn("Invalid delivery ID", "delivery_id", cmd.DeliveryID, "error", err)
		return app.ErrInvalidID
	}

	// Create webhook event with validation
	event, err := domain.NewWebhookEvent(id, cmd.Source, cmd.Payload)
	if err != nil {
		h.logger.Warn("Invalid webhook event", "delivery_id", cmd.DeliveryID, "source", cmd.Source, "error", err)
		return app.ErrInvalidWebhookData
	}

	// Check for duplicates
	isDup, err := h.dedupService.IsDuplicate(ctx, id)
	if err != nil {
		h.logger.Error("Deduplication check failed", "delivery_id", id.String(), "error", err)
		return app.ErrDeduplicationFailed
	}

	if isDup {
		h.logger.Info("Duplicate webhook event ignored", "delivery_id", id.String(), "source", cmd.Source)
		return app.ErrDuplicate
	}

	// Mark as processed before publishing to prevent race conditions
	if err := h.dedupService.MarkProcessed(ctx, id); err != nil {
		h.logger.Error("Failed to mark webhook as processed", "delivery_id", id.String(), "error", err)
		return app.ErrDeduplicationFailed
	}

	// Publish the event
	if err := h.pubService.Publish(ctx, *event); err != nil {
		h.logger.Error("Failed to publish webhook event", "delivery_id", id.String(), "source", cmd.Source, "error", err)
		return app.ErrPublishFailed
	}

	h.logger.Info("Webhook event processed successfully", "delivery_id", id.String(), "source", cmd.Source)
	return nil
}