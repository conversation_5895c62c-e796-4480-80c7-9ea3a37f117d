package app

import (
	"context"
	"template_microservice/internal/domain"
)

// TemplateRepository defines the interface for template persistence
type TemplateRepository interface {
	Create(template *domain.Template) error
	GetByID(id string) (*domain.Template, error)
}

// EventPublisher defines the interface for publishing domain events
type EventPublisher interface {
	PublishTemplateCreated(ctx context.Context, template *domain.Template) error
	PublishTemplateUpdated(ctx context.Context, template *domain.Template) error
	PublishTemplateDeleted(ctx context.Context, templateID string) error
} 