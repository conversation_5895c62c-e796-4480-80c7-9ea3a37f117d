package app

import (
	"context"
	"webhook_microservice/internal/domain"
)

// DeduplicationService defines the interface for webhook deduplication
type DeduplicationService interface {
	IsDuplicate(ctx context.Context, id domain.DeliveryID) (bool, error)
	MarkProcessed(ctx context.Context, id domain.DeliveryID) error
}

// PublishingService defines the interface for publishing webhook events
type PublishingService interface {
	Publish(ctx context.Context, event domain.WebhookEvent) error
}