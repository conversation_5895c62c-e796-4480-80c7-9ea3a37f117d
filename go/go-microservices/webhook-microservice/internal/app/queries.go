package app

import (
	"template_microservice/internal/domain"
)

// GetTemplateQuery represents the query to get a template by ID
type GetTemplateQuery struct {
	ID string
}

// GetTemplateHandler handles retrieving templates
type GetTemplateHandler struct {
	repo TemplateRepository
}

// NewGetTemplateHandler creates a new GetTemplateHandler
func NewGetTemplateHandler(repo TemplateRepository) *GetTemplateHandler {
	return &GetTemplateHandler{
		repo: repo,
	}
}

// Handle processes the GetTemplateQuery
func (h *GetTemplateHandler) Handle(query GetTemplateQuery) (*domain.Template, error) {
	if query.ID == "" {
		return nil, ErrInvalidTemplate
	}

	template, err := h.repo.GetByID(query.ID)
	if err != nil {
		return nil, err
	}

	return template, nil
} 