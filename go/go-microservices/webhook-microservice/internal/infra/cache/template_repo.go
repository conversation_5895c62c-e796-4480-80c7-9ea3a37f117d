package cache

import (
	"context"
	"fmt"

	"template_microservice/internal/app"
	"template_microservice/internal/domain"
	"template_microservice/internal/infra/redis"
)

type TemplateRepository struct {
	repo  app.TemplateRepository
	cache *redis.Cache
}

func NewTemplateRepository(repo app.TemplateRepository, cache *redis.Cache) *TemplateRepository {
	return &TemplateRepository{
		repo:  repo,
		cache: cache,
	}
}

func (r *TemplateRepository) Create(template *domain.Template) error {
	// Create in the underlying repository
	if err := r.repo.Create(template); err != nil {
		return err
	}

	// Cache the template
	ctx := context.Background()
	if err := r.cache.Set(ctx, template); err != nil {
		// Log the error but don't fail the operation
		fmt.Printf("Failed to cache template: %v\n", err)
	}

	return nil
}

func (r *TemplateRepository) GetByID(id string) (*domain.Template, error) {
	// Try to get from cache first
	ctx := context.Background()
	if template, err := r.cache.Get(ctx, id); err == nil && template != nil {
		return template, nil
	}

	// If not in cache, get from repository
	template, err := r.repo.GetByID(id)
	if err != nil {
		return nil, err
	}

	// Cache the template for future requests
	if err := r.cache.Set(ctx, template); err != nil {
		// Log the error but don't fail the operation
		fmt.Printf("Failed to cache template: %v\n", err)
	}

	return template, nil
} 