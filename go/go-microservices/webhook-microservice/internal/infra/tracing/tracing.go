package tracing

import (
	"github.com/Matrics-io/the-manhattan-project-huly/go/shared/tracing"
)

// Config wraps the shared tracing config for webhook microservice
type Config struct {
	ServiceName    string
	JaegerEndpoint string
	Enabled        bool
}

// Initialize sets up tracing using the shared tracing package
func Initialize(cfg Config) (func(), error) {
	sharedConfig := tracing.Config{
		ServiceName:    cfg.ServiceName,
		JaegerEndpoint: cfg.JaegerEndpoint,
		Enabled:        cfg.Enabled,
	}
	
	return tracing.Initialize(sharedConfig)
}

// Middleware returns the shared tracing middleware
func Middleware(serviceName string) func() {
	return func() {
		// The shared tracing middleware is applied in the router
		// This is just a placeholder for webhook-specific tracing setup
	}
}
