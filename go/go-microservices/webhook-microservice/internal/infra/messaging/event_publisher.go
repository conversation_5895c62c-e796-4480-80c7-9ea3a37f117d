package messaging

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	daprclient "github.com/dapr/go-sdk/client"
	"template_microservice/internal/app"
	"template_microservice/internal/domain"
)

// DaprEventPublisher implements EventPublisher using Dapr pub/sub
type DaprEventPublisher struct {
	client      daprclient.Client
	pubsubName  string
	appID       string
	logger      *slog.Logger
}

// NewDaprEventPublisher creates a new Dapr-based event publisher
func NewDaprEventPublisher(client daprclient.Client, pubsubName, appID string, logger *slog.Logger) (app.EventPublisher, error) {
	if client == nil {
		return nil, fmt.Errorf("dapr client cannot be nil")
	}
	if pubsubName == "" {
		return nil, fmt.Errorf("pubsub name cannot be empty")
	}
	if appID == "" {
		return nil, fmt.Errorf("app ID cannot be empty")
	}

	return &DaprEventPublisher{
		client:     client,
		pubsubName: pubsubName,
		appID:      appID,
		logger:     logger,
	}, nil
}

// PublishTemplateCreated publishes a template created event
func (p *DaprEventPublisher) PublishTemplateCreated(ctx context.Context, template *domain.Template) error {
	event := map[string]interface{}{
		"eventType":   "template.created",
		"templateId":  template.ID,
		"templateName": template.Name,
		"timestamp":   time.Now().UTC(),
		"source":      p.appID,
		"version":     "1.0",
	}

	return p.publishEvent(ctx, "template-events", event)
}

// PublishTemplateUpdated publishes a template updated event
func (p *DaprEventPublisher) PublishTemplateUpdated(ctx context.Context, template *domain.Template) error {
	event := map[string]interface{}{
		"eventType":   "template.updated",
		"templateId":  template.ID,
		"templateName": template.Name,
		"timestamp":   time.Now().UTC(),
		"source":      p.appID,
		"version":     "1.0",
	}

	return p.publishEvent(ctx, "template-events", event)
}

// PublishTemplateDeleted publishes a template deleted event
func (p *DaprEventPublisher) PublishTemplateDeleted(ctx context.Context, templateID string) error {
	event := map[string]interface{}{
		"eventType":  "template.deleted",
		"templateId": templateID,
		"timestamp":  time.Now().UTC(),
		"source":     p.appID,
		"version":    "1.0",
	}

	return p.publishEvent(ctx, "template-events", event)
}

// publishEvent is a helper method to publish events to Dapr
func (p *DaprEventPublisher) publishEvent(ctx context.Context, topic string, event map[string]interface{}) error {
	err := p.client.PublishEvent(ctx, p.pubsubName, topic, event)
	if err != nil {
		p.logger.Error("Failed to publish event",
			"error", err,
			"topic", topic,
			"pubsub", p.pubsubName,
			"eventType", event["eventType"],
		)
		return fmt.Errorf("failed to publish event to topic %s: %w", topic, err)
	}

	p.logger.Info("Event published successfully",
		"topic", topic,
		"pubsub", p.pubsubName,
		"eventType", event["eventType"],
		"templateId", event["templateId"],
	)

	return nil
} 