package dapr

import (
	"context"
	"fmt"

	dapr "github.com/dapr/go-sdk/client"
	"go.uber.org/zap"
	"template_microservice/internal/config"
)

// SecretsClient handles Dapr secrets management operations
type SecretsClient struct {
	client dapr.Client
	config *config.Config
	logger *zap.Logger
}

// NewSecretsClient creates a new Dapr secrets client
func NewSecretsClient(client dapr.Client, cfg *config.Config, logger *zap.Logger) *SecretsClient {
	return &SecretsClient{
		client: client,
		config: cfg,
		logger: logger,
	}
}

// GetSecret retrieves a secret from Dapr secret store or falls back to config
func (s *SecretsClient) GetSecret(ctx context.Context, storeName, key string) (map[string]string, error) {
	// For now, we're using config-based secrets since they work perfectly with YAML
	// In production, you would configure a proper secret store (Azure Key Vault, AWS Secrets Manager, etc.)
	
	secret, err := s.config.GetSecret(key)
	if err != nil {
		return nil, fmt.Errorf("failed to get secret from config: %w", err)
	}

	s.logger.Info("Secret retrieved from YAML config",
		zap.String("key", key),
	)

	return secret, nil
}



// GetSecretValue retrieves a specific secret value
func (s *SecretsClient) GetSecretValue(ctx context.Context, storeName, key, secretKey string) (string, error) {
	secrets, err := s.GetSecret(ctx, storeName, key)
	if err != nil {
		return "", err
	}

	value, exists := secrets[secretKey]
	if !exists {
		return "", fmt.Errorf("secret key '%s' not found in secret '%s'", secretKey, key)
	}

	return value, nil
}

// GetBulkSecret retrieves multiple secrets at once
func (s *SecretsClient) GetBulkSecret(ctx context.Context, storeName string) (map[string]map[string]string, error) {
	if s.client != nil {
		secrets, err := s.client.GetBulkSecret(ctx, storeName, nil)
		if err == nil {
			s.logger.Info("Bulk secrets retrieved from Dapr",
				zap.String("store", storeName),
				zap.Int("count", len(secrets)),
			)
			return secrets, nil
		}
		
		s.logger.Warn("Failed to get bulk secrets from Dapr, falling back to config",
			zap.Error(err),
			zap.String("store", storeName),
		)
	}

	// Fallback to config-based secrets
	allSecrets := make(map[string]map[string]string)
	
	// Get all secret categories from config
	if dbSecrets, err := s.config.GetSecret("database"); err == nil {
		allSecrets["database"] = dbSecrets
	}
	if apiSecrets, err := s.config.GetSecret("api"); err == nil {
		allSecrets["api"] = apiSecrets
	}
	if extSecrets, err := s.config.GetSecret("external"); err == nil {
		allSecrets["external"] = extSecrets
	}

	s.logger.Info("Bulk secrets retrieved from config",
		zap.Int("count", len(allSecrets)),
	)

	return allSecrets, nil
}

// GetDatabaseConnectionString is a convenience method for getting database credentials
func (s *SecretsClient) GetDatabaseConnectionString(ctx context.Context) (string, error) {
	return s.GetSecretValue(ctx, s.config.Dapr.SecretStore, "database", "connection_string")
}

// GetAPICredentials is a convenience method for getting API credentials
func (s *SecretsClient) GetAPICredentials(ctx context.Context) (key, secret string, err error) {
	secrets, err := s.GetSecret(ctx, s.config.Dapr.SecretStore, "api")
	if err != nil {
		return "", "", err
	}
	
	return secrets["key"], secrets["secret"], nil
} 