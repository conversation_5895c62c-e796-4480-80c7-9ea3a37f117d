package dapr

import (
	"context"
	"encoding/json"
	"fmt"

	dapr "github.com/dapr/go-sdk/client"
	"go.uber.org/zap"
)

// InvocationClient handles Dapr service invocation operations
type InvocationClient struct {
	client dapr.Client
	logger *zap.Logger
}

// NewInvocationClient creates a new Dapr service invocation client
func NewInvocationClient(client dapr.Client, logger *zap.Logger) *InvocationClient {
	return &InvocationClient{
		client: client,
		logger: logger,
	}
}

// InvokeMethod invokes a method on another service via Dapr
func (i *InvocationClient) InvokeMethod(ctx context.Context, serviceID, method string, data interface{}) ([]byte, error) {
	if i.client == nil {
		return nil, fmt.Errorf("dapr client not initialized")
	}

	// If we have data, use InvokeMethodWithContent instead
	if data != nil {
		content, err := json.Marshal(data)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request data: %w", err)
		}
		return i.InvokeMethodWithContent(ctx, serviceID, method, content)
	}

	resp, err := i.client.InvokeMethod(ctx, serviceID, method, "post")
	if err != nil {
		i.logger.Error("Service invocation failed",
			zap.Error(err),
			zap.String("service", serviceID),
			zap.String("method", method),
		)
		return nil, fmt.Errorf("failed to invoke service method: %w", err)
	}

	i.logger.Info("Service method invoked successfully",
		zap.String("service", serviceID),
		zap.String("method", method),
	)

	return resp, nil
}

// InvokeMethodWithContent invokes a method with specific content
func (i *InvocationClient) InvokeMethodWithContent(ctx context.Context, serviceID, method string, content []byte) ([]byte, error) {
	if i.client == nil {
		return nil, fmt.Errorf("dapr client not initialized")
	}

	resp, err := i.client.InvokeMethodWithContent(ctx, serviceID, method, "post", &dapr.DataContent{
		Data:        content,
		ContentType: "application/json",
	})
	if err != nil {
		i.logger.Error("Service invocation with content failed",
			zap.Error(err),
			zap.String("service", serviceID),
			zap.String("method", method),
		)
		return nil, fmt.Errorf("failed to invoke service method with content: %w", err)
	}

	i.logger.Info("Service method with content invoked successfully",
		zap.String("service", serviceID),
		zap.String("method", method),
	)

	return resp, nil
}

// InvokeBinding invokes an external binding
func (i *InvocationClient) InvokeBinding(ctx context.Context, bindingName, operation string, data interface{}) ([]byte, error) {
	if i.client == nil {
		return nil, fmt.Errorf("dapr client not initialized")
	}

	var content []byte
	var err error

	if data != nil {
		content, err = json.Marshal(data)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal binding data: %w", err)
		}
	}

	req := &dapr.InvokeBindingRequest{
		Name:      bindingName,
		Operation: operation,
		Data:      content,
	}

	resp, err := i.client.InvokeBinding(ctx, req)
	if err != nil {
		i.logger.Error("Binding invocation failed",
			zap.Error(err),
			zap.String("binding", bindingName),
			zap.String("operation", operation),
		)
		return nil, fmt.Errorf("failed to invoke binding: %w", err)
	}

	i.logger.Info("Binding invoked successfully",
		zap.String("binding", bindingName),
		zap.String("operation", operation),
	)

	return resp.Data, nil
} 