package dapr

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	dapr "github.com/dapr/go-sdk/client"
	"webhook_microservice/internal/domain"
)

// StateClient handles Dapr state management operations
type StateClient struct {
	client dapr.Client
}

// NewStateClient creates a new Dapr state client
func NewStateClient(client dapr.Client) *StateClient {
	return &StateClient{
		client: client,
	}
}

// GetState retrieves a state from Dapr state store using HTTP API as workaround
func (s *StateClient) GetState(ctx context.Context, storeName, key string) ([]byte, error) {
	if s.client == nil {
		return nil, fmt.Errorf("dapr client not initialized")
	}

	// Use HTTP API as workaround for Go SDK issue
	url := fmt.Sprintf("http://localhost:3500/v1.0/state/%s/%s", storeName, key)

	// Create HTTP request with proper context
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get state via HTTP: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == 404 {
		// Key doesn't exist, return empty
		return []byte{}, nil
	}

	if resp.StatusCode != 200 {
		// Read response body for debugging
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("HTTP error: %d, body: %s", resp.StatusCode, string(body))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Remove JSON quotes if present
	if len(body) >= 2 && body[0] == '"' && body[len(body)-1] == '"' {
		body = body[1 : len(body)-1]
	}

	return body, nil
}

// SaveState saves a state to Dapr state store using HTTP API as workaround
func (s *StateClient) SaveState(ctx context.Context, storeName, key string, value interface{}) error {
	if s.client == nil {
		return fmt.Errorf("dapr client not initialized")
	}

	// Use HTTP API as workaround for Go SDK issue
	url := fmt.Sprintf("http://localhost:3500/v1.0/state/%s", storeName)

	// Create the state array payload
	stateData := []map[string]interface{}{
		{
			"key":   key,
			"value": value,
		},
	}

	data, err := json.Marshal(stateData)
	if err != nil {
		return fmt.Errorf("failed to marshal state data: %w", err)
	}

	// Create HTTP request with proper context and headers
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(data))
	if err != nil {
		return fmt.Errorf("failed to create HTTP request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("dapr-app-id", "webhook-microservice")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to save state via HTTP: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 204 && resp.StatusCode != 200 {
		// Read response body for debugging
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("HTTP error: %d, body: %s", resp.StatusCode, string(body))
	}

	return nil
}

// DeleteState deletes a state from Dapr state store
func (s *StateClient) DeleteState(ctx context.Context, storeName, key string) error {
	if s.client == nil {
		return fmt.Errorf("dapr client not initialized")
	}

	err := s.client.DeleteState(ctx, storeName, key, nil)
	if err != nil {
		return fmt.Errorf("failed to delete state: %w", err)
	}

	return nil
}

// GetStateWithMetadata retrieves state with metadata
func (s *StateClient) GetStateWithMetadata(ctx context.Context, storeName, key string) (*dapr.StateItem, error) {
	if s.client == nil {
		return nil, fmt.Errorf("dapr client not initialized")
	}

	item, err := s.client.GetState(ctx, storeName, key, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get state with metadata: %w", err)
	}

	return item, nil
}

// WebhookDeduplicationService implements DeduplicationService using Dapr state store
type WebhookDeduplicationService struct {
	client    *StateClient
	storeName string
}

// NewWebhookDeduplicationService creates a new webhook deduplication service
func NewWebhookDeduplicationService(client *StateClient, storeName string) *WebhookDeduplicationService {
	return &WebhookDeduplicationService{
		client:    client,
		storeName: storeName,
	}
}

// IsDuplicate checks if a delivery ID has already been processed
func (w *WebhookDeduplicationService) IsDuplicate(ctx context.Context, id domain.DeliveryID) (bool, error) {
	key := fmt.Sprintf("webhook:processed:%s", id.String())

	data, err := w.client.GetState(ctx, w.storeName, key)
	if err != nil {
		return false, fmt.Errorf("failed to check duplicate: %w", err)
	}

	// If data exists, it's a duplicate
	return len(data) > 0, nil
}

// MarkProcessed marks a delivery ID as processed
func (w *WebhookDeduplicationService) MarkProcessed(ctx context.Context, id domain.DeliveryID) error {
	key := fmt.Sprintf("webhook:processed:%s", id.String())

	// Store a simple timestamp string to mark as processed
	processedAt := time.Now().UTC().Format(time.RFC3339)

	err := w.client.SaveState(ctx, w.storeName, key, processedAt)
	if err != nil {
		return fmt.Errorf("failed to mark as processed: %w", err)
	}

	return nil
}