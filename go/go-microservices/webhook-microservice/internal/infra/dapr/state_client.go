package dapr

import (
	"context"
	"encoding/json"
	"fmt"

	dapr "github.com/dapr/go-sdk/client"
)

// StateClient handles Dapr state management operations
type StateClient struct {
	client dapr.Client
}

// NewStateClient creates a new Dapr state client
func NewStateClient(client dapr.Client) *StateClient {
	return &StateClient{
		client: client,
	}
}

// GetState retrieves a state from Dapr state store
func (s *StateClient) GetState(ctx context.Context, storeName, key string) ([]byte, error) {
	if s.client == nil {
		return nil, fmt.Errorf("dapr client not initialized")
	}

	item, err := s.client.GetState(ctx, storeName, key, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get state: %w", err)
	}

	return item.Value, nil
}

// SaveState saves a state to Dapr state store
func (s *StateClient) SaveState(ctx context.Context, storeName, key string, value interface{}) error {
	if s.client == nil {
		return fmt.Errorf("dapr client not initialized")
	}

	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal value: %w", err)
	}

	err = s.client.SaveState(ctx, storeName, key, data, nil)
	if err != nil {
		return fmt.Errorf("failed to save state: %w", err)
	}

	return nil
}

// DeleteState deletes a state from Dapr state store
func (s *StateClient) DeleteState(ctx context.Context, storeName, key string) error {
	if s.client == nil {
		return fmt.Errorf("dapr client not initialized")
	}

	err := s.client.DeleteState(ctx, storeName, key, nil)
	if err != nil {
		return fmt.Errorf("failed to delete state: %w", err)
	}

	return nil
}

// GetStateWithMetadata retrieves state with metadata
func (s *StateClient) GetStateWithMetadata(ctx context.Context, storeName, key string) (*dapr.StateItem, error) {
	if s.client == nil {
		return nil, fmt.Errorf("dapr client not initialized")
	}

	item, err := s.client.GetState(ctx, storeName, key, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get state with metadata: %w", err)
	}

	return item, nil
} 