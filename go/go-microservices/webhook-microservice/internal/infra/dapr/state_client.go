package dapr

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	dapr "github.com/dapr/go-sdk/client"
	"webhook_microservice/internal/domain"
)

// StateClient handles Dapr state management operations
type StateClient struct {
	client dapr.Client
}

// NewStateClient creates a new Dapr state client
func NewStateClient(client dapr.Client) *StateClient {
	return &StateClient{
		client: client,
	}
}

// GetState retrieves a state from Dapr state store
func (s *StateClient) GetState(ctx context.Context, storeName, key string) ([]byte, error) {
	if s.client == nil {
		return nil, fmt.Errorf("dapr client not initialized")
	}

	item, err := s.client.GetState(ctx, storeName, key, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get state: %w", err)
	}

	return item.Value, nil
}

// SaveState saves a state to Dapr state store
func (s *StateClient) SaveState(ctx context.Context, storeName, key string, value interface{}) error {
	if s.client == nil {
		return fmt.Errorf("dapr client not initialized")
	}

	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal value: %w", err)
	}

	err = s.client.SaveState(ctx, storeName, key, data, nil)
	if err != nil {
		return fmt.Errorf("failed to save state: %w", err)
	}

	return nil
}

// DeleteState deletes a state from Dapr state store
func (s *StateClient) DeleteState(ctx context.Context, storeName, key string) error {
	if s.client == nil {
		return fmt.Errorf("dapr client not initialized")
	}

	err := s.client.DeleteState(ctx, storeName, key, nil)
	if err != nil {
		return fmt.Errorf("failed to delete state: %w", err)
	}

	return nil
}

// GetStateWithMetadata retrieves state with metadata
func (s *StateClient) GetStateWithMetadata(ctx context.Context, storeName, key string) (*dapr.StateItem, error) {
	if s.client == nil {
		return nil, fmt.Errorf("dapr client not initialized")
	}

	item, err := s.client.GetState(ctx, storeName, key, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get state with metadata: %w", err)
	}

	return item, nil
}

// WebhookDeduplicationService implements DeduplicationService using Dapr state store
type WebhookDeduplicationService struct {
	client    *StateClient
	storeName string
}

// NewWebhookDeduplicationService creates a new webhook deduplication service
func NewWebhookDeduplicationService(client *StateClient, storeName string) *WebhookDeduplicationService {
	return &WebhookDeduplicationService{
		client:    client,
		storeName: storeName,
	}
}

// IsDuplicate checks if a delivery ID has already been processed
func (w *WebhookDeduplicationService) IsDuplicate(ctx context.Context, id domain.DeliveryID) (bool, error) {
	key := fmt.Sprintf("webhook:processed:%s", id.String())

	data, err := w.client.GetState(ctx, w.storeName, key)
	if err != nil {
		return false, fmt.Errorf("failed to check duplicate: %w", err)
	}

	// If data exists, it's a duplicate
	return len(data) > 0, nil
}

// MarkProcessed marks a delivery ID as processed
func (w *WebhookDeduplicationService) MarkProcessed(ctx context.Context, id domain.DeliveryID) error {
	key := fmt.Sprintf("webhook:processed:%s", id.String())

	// Store a simple timestamp to mark as processed
	processedAt := time.Now().UTC()

	err := w.client.SaveState(ctx, w.storeName, key, processedAt)
	if err != nil {
		return fmt.Errorf("failed to mark as processed: %w", err)
	}

	return nil
}