package dapr

import (
	"context"
	"fmt"

	dapr "github.com/dapr/go-sdk/client"
	"go.uber.org/zap"
	"template_microservice/internal/config"
)

// Client represents a comprehensive Dapr client with all capabilities
type Client struct {
	daprClient dapr.Client
	config     *config.Config
	State      *StateClient
	PubSub     *PubSubClient
	Invocation *InvocationClient
	Secrets    *SecretsClient
	logger     *zap.Logger
}

// NewClient creates a new comprehensive Dapr client
func NewClient(cfg *config.Config, logger *zap.Logger) (*Client, error) {
	var daprClient dapr.Client
	var err error

	// Only create Dapr client if enabled
	if cfg.Dapr.Enabled {
		daprClient, err = dapr.NewClient()
		if err != nil {
			logger.Warn("Failed to create Dapr client, running without Dapr", zap.Error(err))
			daprClient = nil
		} else {
			logger.Info("Dapr client initialized successfully")
		}
	} else {
		logger.Info("Dapr is disabled in configuration")
	}

	// Create specialized clients (they handle nil daprClient gracefully)
	client := &Client{
		daprClient: daprClient,
		config:     cfg,
		State:      NewStateClient(daprClient),
		PubSub:     NewPubSubClient(daprClient, logger),
		Invocation: NewInvocationClient(daprClient, logger),
		Secrets:    NewSecretsClient(daprClient, cfg, logger),
		logger:     logger,
	}

	return client, nil
}

// Close closes the Dapr client and all connections
func (c *Client) Close() error {
	if c.daprClient != nil {
		c.logger.Info("Closing Dapr client")
		c.daprClient.Close()
	}
	return nil
}

// GetDaprClient returns the underlying Dapr client for advanced usage
func (c *Client) GetDaprClient() dapr.Client {
	return c.daprClient
}

// HealthCheck performs a health check on the Dapr sidecar
func (c *Client) HealthCheck(ctx context.Context) error {
	if c.daprClient == nil {
		return fmt.Errorf("dapr client not initialized")
	}

	// Try to get state from a dummy store to test connectivity
	_, err := c.State.GetState(ctx, c.config.Dapr.StateStore, "health-check")
	if err != nil {
		// This is expected if the key doesn't exist, but it means Dapr is responding
		c.logger.Debug("Dapr health check completed", zap.Error(err))
	}

	return nil
}

// IsEnabled returns whether Dapr is enabled and available
func (c *Client) IsEnabled() bool {
	return c.config.Dapr.Enabled && c.daprClient != nil
} 