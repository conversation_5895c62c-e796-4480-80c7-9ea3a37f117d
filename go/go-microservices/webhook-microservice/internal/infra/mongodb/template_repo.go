package mongodb

import (
	"context"
	"fmt"
	"time"

	"template_microservice/internal/app"
	"template_microservice/internal/domain"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type TemplateRepository struct {
	collection *mongo.Collection
}

func NewTemplateRepository(db *mongo.Database) (*TemplateRepository, error) {
	if db == nil {
		return nil, fmt.Errorf("database instance cannot be nil")
	}

	collection := db.Collection("templates")

	// Create indexes
	indexModel := mongo.IndexModel{
		Keys:    bson.D{{Key: "id", Value: 1}},
		Options: options.Index().SetUnique(true),
	}
	if _, err := collection.Indexes().CreateOne(context.Background(), indexModel); err != nil {
		return nil, fmt.Errorf("failed to create index: %w", err)
	}

	return &TemplateRepository{
		collection: collection,
	}, nil
}

func (r *TemplateRepository) Create(template *domain.Template) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	doc := bson.M{
		"id":         template.ID,
		"name":       template.Name,
		"content":    template.Content,
		"created_at": template.CreatedAt,
		"updated_at": template.UpdatedAt,
	}

	_, err := r.collection.InsertOne(ctx, doc)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return app.ErrTemplateAlreadyExists
		}
		return fmt.Errorf("failed to insert template: %w", err)
	}

	return nil
}

func (r *TemplateRepository) GetByID(id string) (*domain.Template, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	var template domain.Template
	err := r.collection.FindOne(ctx, bson.M{"id": id}).Decode(&template)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, app.ErrTemplateNotFound
		}
		return nil, fmt.Errorf("failed to find template: %w", err)
	}

	return &template, nil
} 