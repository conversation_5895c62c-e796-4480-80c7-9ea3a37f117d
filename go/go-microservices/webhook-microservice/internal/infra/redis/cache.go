package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"template_microservice/internal/domain"
	"github.com/redis/go-redis/v9"
)

type Cache struct {
	client     *redis.Client
	keyPrefix  string
	defaultTTL time.Duration
}

func NewCache(addr, password string, db, poolSize, minIdleConns, maxRetries int, keyPrefix string, defaultTTL time.Duration) (*Cache, error) {
	client := redis.NewClient(&redis.Options{
		Addr:            addr,
		Password:        password,
		DB:              db,
		PoolSize:        poolSize,
		MinIdleConns:    minIdleConns,
		MaxRetries:      maxRetries,
		ConnMaxLifetime: time.Hour,
	})

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	return &Cache{
		client:     client,
		keyPrefix:  keyPrefix,
		defaultTTL: defaultTTL,
	}, nil
}

func (c *Cache) Get(ctx context.Context, id string) (*domain.Template, error) {
	key := c.keyPrefix + id
	data, err := c.client.Get(ctx, key).Bytes()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get template from cache: %w", err)
	}

	var template domain.Template
	if err := json.Unmarshal(data, &template); err != nil {
		return nil, fmt.Errorf("failed to unmarshal template: %w", err)
	}

	return &template, nil
}

func (c *Cache) Set(ctx context.Context, template *domain.Template) error {
	key := c.keyPrefix + template.ID
	data, err := json.Marshal(template)
	if err != nil {
		return fmt.Errorf("failed to marshal template: %w", err)
	}

	if err := c.client.Set(ctx, key, data, c.defaultTTL).Err(); err != nil {
		return fmt.Errorf("failed to set template in cache: %w", err)
	}

	return nil
}

func (c *Cache) Delete(ctx context.Context, id string) error {
	key := c.keyPrefix + id
	if err := c.client.Del(ctx, key).Err(); err != nil {
		return fmt.Errorf("failed to delete template from cache: %w", err)
	}
	return nil
}

func (c *Cache) Close() error {
	return c.client.Close()
} 