package clickhouse

import (
	"database/sql"
	"fmt"

	_ "github.com/ClickHouse/clickhouse-go/v2"
	"template_microservice/internal/app"
	"template_microservice/internal/domain"
)

type TemplateRepository struct {
	db *sql.DB
}

func NewTemplateRepository(db *sql.DB) (*TemplateRepository, error) {
	if db == nil {
		return nil, fmt.Errorf("db instance cannot be nil")
	}
	return &TemplateRepository{db: db}, nil
}

func (r *TemplateRepository) Create(template *domain.Template) error {
	_, err := r.db.Exec(
		`INSERT INTO templates.templates (id, name, content, created_at, updated_at) VALUES (?, ?, ?, ?, ?)`,
		template.ID, template.Name, template.Content, template.CreatedAt, template.UpdatedAt,
	)
	return err
}

func (r *TemplateRepository) GetByID(id string) (*domain.Template, error) {
	row := r.db.QueryRow(
		`SELECT id, name, content, created_at, updated_at FROM templates.templates WHERE id = ?`, id,
	)
	var t domain.Template
	if err := row.Scan(&t.ID, &t.Name, &t.Content, &t.CreatedAt, &t.UpdatedAt); err != nil {
		if err == sql.ErrNoRows {
			return nil, app.ErrTemplateNotFound
		}
		return nil, err
	}
	return &t, nil
} 