package clickhouse

import (
	"database/sql"
	"fmt"

	_ "github.com/ClickHouse/clickhouse-go/v2"
)

// Client represents a ClickHouse client
type Client struct {
	db *sql.DB
}

// NewClient creates a new ClickHouse client
func NewClient(dsn string) (*Client, error) {
	db, err := sql.Open("clickhouse", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to ClickHouse: %w", err)
	}

	// Test connection
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping ClickHouse: %w", err)
	}

	return &Client{db: db}, nil
}

// Close closes the ClickHouse connection
func (c *Client) Close() error {
	return c.db.Close()
}

// DB returns the underlying sql.DB
func (c *Client) DB() *sql.DB {
	return c.db
} 