package logging

import (
	"os"
	"sync"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

var (
	globalLogger *Logger
	once        sync.Once
)

// Logger wraps zap.Logger to provide a simpler interface
type Logger struct {
	*zap.Logger
}

// NewLogger creates a new logger instance
func NewLogger(level string) (*Logger, error) {
	// Parse the log level
	var zapLevel zapcore.Level
	if err := zapLevel.UnmarshalText([]byte(level)); err != nil {
		return nil, err
	}

	// Create the encoder config
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		Caller<PERSON><PERSON>:      "caller",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.LowercaseLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	// Create the core
	core := zapcore.NewCore(
		zapcore.NewJSONEncoder(encoderConfig),
		zapcore.AddSync(os.Stdout),
		zapLevel,
	)

	// Create the logger
	logger := zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))

	// Set the global logger
	once.Do(func() {
		globalLogger = &Logger{logger}
	})

	return &Logger{logger}, nil
}

// GetLogger returns the global logger instance
func GetLogger() *Logger {
	if globalLogger == nil {
		// Create a default logger if none exists
		logger, _ := NewLogger("info")
		return logger
	}
	return globalLogger
}

// WithRequestID adds a request ID to the logger
func (l *Logger) WithRequestID(requestID string) *Logger {
	return &Logger{l.With(zap.String("request_id", requestID))}
}

// WithError adds an error to the logger
func (l *Logger) WithError(err error) *Logger {
	return &Logger{l.With(zap.Error(err))}
} 

// WithGroup adds a component group to the logger for scoped logging
func (l *Logger) WithGroup(group string) *Logger {
	return &Logger{l.With(zap.String("component", group))}
} 