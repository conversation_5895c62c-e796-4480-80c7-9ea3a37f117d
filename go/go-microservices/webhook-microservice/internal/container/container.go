package container

import (
	"context"
	"log/slog"
	"os"
	"strings"
	"sync"

	"github.com/gin-gonic/gin"
	daprclient "github.com/dapr/go-sdk/client"
	"go.opentelemetry.io/otel/trace"

	"webhook_microservice/internal/config"
	"webhook_microservice/internal/infra/dapr"
	"webhook_microservice/internal/logging"
	"webhook_microservice/internal/observability"
)

// Config represents the container's simplified configuration
type Config struct {
	// Basic settings
	HTTPPort, GinMode, Environment string
	LogLevel                       slog.Level

	// Webhook microservice doesn't need traditional databases

	// Dapr settings
	DaprEnabled                                                 bool
	DaprAppID, DaprPubSubName, DaprStateStore, DaprSecretStore string

	// Observability settings
	PrometheusEnabled, TracingEnabled bool
	JaegerEndpoint                    string
}

// Container holds all the application dependencies
type Container struct {
	// Configuration embedded directly
	config Config

	// Core infrastructure
	logger *slog.Logger
	tracer trace.Tracer

	// Webhook microservice uses only Dapr for state and messaging

	// Dapr clients (individual fields)
	daprClient       daprclient.Client
	stateClient      *dapr.StateClient
	pubsubClient     *dapr.PubSubClient
	invocationClient *dapr.InvocationClient
	secretsClient    *dapr.SecretsClient

	// Webhook services will be created on-demand in handlers

	// Initialization control
	initOnce sync.Once
	err      error

	// Cleanup functions
	tracerCleanup func()
}

// New creates a new container with all dependencies wired together
func New() (*Container, error) {
	c := &Container{}
	c.loadConfig()
	c.initLogger()

	c.initOnce.Do(func() {
		c.initTracer()
		c.initDaprClients()
	})

	if c.err != nil {
		c.logger.Error("Container initialization failed", "error", c.err)
		return nil, c.err
	}

	c.logger.Info("Container initialized successfully")
	return c, nil
}

// loadConfig loads configuration from environment variables
func (c *Container) loadConfig() {
	// Environment-first configuration loading
	c.config.HTTPPort = getEnvOrDefault("HTTP_PORT", "8080")
	c.config.GinMode = getEnvOrDefault("GIN_MODE", gin.DebugMode)
	c.config.Environment = getEnvOrDefault("ENVIRONMENT", "development")

	// Parse log level
	logLevel := getEnvOrDefault("LOG_LEVEL", "info")
	switch strings.ToLower(logLevel) {
	case "debug":
		c.config.LogLevel = slog.LevelDebug
	case "warn":
		c.config.LogLevel = slog.LevelWarn
	case "error":
		c.config.LogLevel = slog.LevelError
	default:
		c.config.LogLevel = slog.LevelInfo
	}

	// Webhook microservice doesn't need traditional databases

	// Dapr settings
	c.config.DaprEnabled = getEnvOrDefault("DAPR_ENABLED", "true") == "true"
	c.config.DaprAppID = getEnvOrDefault("DAPR_APP_ID", "webhook-microservice")
	c.config.DaprPubSubName = getEnvOrDefault("DAPR_PUBSUB_NAME", "redis-pubsub")
	c.config.DaprStateStore = getEnvOrDefault("DAPR_STATE_STORE", "statestore")
	c.config.DaprSecretStore = getEnvOrDefault("DAPR_SECRET_STORE", "config-secret-store")

	// Observability settings
	c.config.PrometheusEnabled = getEnvOrDefault("PROMETHEUS_ENABLED", "true") == "true"
	c.config.TracingEnabled = getEnvOrDefault("TRACING_ENABLED", "false") == "true"
	c.config.JaegerEndpoint = getEnvOrDefault("JAEGER_ENDPOINT", "")
}

// initLogger initializes the structured logger
func (c *Container) initLogger() {
	opts := &slog.HandlerOptions{
		Level:     c.config.LogLevel,
		AddSource: c.config.GinMode == gin.DebugMode,
	}
	c.logger = slog.New(slog.NewJSONHandler(os.Stdout, opts)).With("service", "webhook-microservice")
	slog.SetDefault(c.logger)
}

// initTracer initializes OpenTelemetry tracing if enabled
func (c *Container) initTracer() {
	if c.err != nil {
		return
	}

	if !c.config.TracingEnabled || c.config.JaegerEndpoint == "" {
		c.logger.Info("Tracing disabled or no Jaeger endpoint configured")
		return
	}

	cleanup, err := observability.InitTracer("webhook-microservice", c.config.JaegerEndpoint)
	if err != nil {
		c.logger.Warn("Failed to initialize tracer, continuing without tracing", "error", err)
		return
	}

	c.tracer = observability.GetTracer("webhook-microservice")
	c.tracerCleanup = cleanup
	c.logger.Info("Tracer initialized successfully")
}



// initDaprClients initializes Dapr clients with graceful degradation
func (c *Container) initDaprClients() {
	if c.err != nil {
		return
	}

	if !c.config.DaprEnabled {
		c.logger.Info("Dapr disabled in configuration")
		return
	}

	var err error
	c.daprClient, err = daprclient.NewClient()
	if err != nil {
		c.logger.Warn("Failed to initialize Dapr client, continuing without Dapr", "error", err)
		return
	}

	// Create a zap logger for Dapr clients (they expect zap.Logger)
	zapLogger, err := logging.NewLogger(strings.ToLower(c.config.LogLevel.String()))
	if err != nil {
		c.logger.Warn("Failed to create zap logger for Dapr clients", "error", err)
		return
	}

	// Initialize individual Dapr clients
	c.stateClient = dapr.NewStateClient(c.daprClient)
	c.pubsubClient = dapr.NewPubSubClient(c.daprClient, zapLogger.WithGroup("PubSub").Logger)
	c.invocationClient = dapr.NewInvocationClient(c.daprClient, zapLogger.WithGroup("Invocation").Logger)
	
	// Create a temporary config for secrets client
	tempConfig := &config.Config{}
	tempConfig.Dapr.SecretStore = c.config.DaprSecretStore
	c.secretsClient = dapr.NewSecretsClient(c.daprClient, tempConfig, zapLogger.WithGroup("Secrets").Logger)

	c.logger.Info("Dapr clients initialized successfully")
}







// Accessor methods
func (c *Container) Logger() *slog.Logger                     { return c.logger }
func (c *Container) Config() Config                           { return c.config }
func (c *Container) DaprClient() daprclient.Client            { return c.daprClient }
func (c *Container) StateClient() *dapr.StateClient           { return c.stateClient }
func (c *Container) PubSubClient() *dapr.PubSubClient         { return c.pubsubClient }
func (c *Container) InvocationClient() *dapr.InvocationClient { return c.invocationClient }
func (c *Container) SecretsClient() *dapr.SecretsClient       { return c.secretsClient }

// HealthCheck performs a health check on all available components
func (c *Container) HealthCheck(ctx context.Context) error {
	// Check Dapr
	if c.daprClient != nil && c.stateClient != nil {
		if _, err := c.stateClient.GetState(ctx, c.config.DaprStateStore, "health-check"); err != nil {
			// This is expected if the key doesn't exist, but it means Dapr is responding
			c.logger.Debug("Dapr health check completed", "error", err)
		}
	}

	return nil
}

// Close closes all connections and resources
func (c *Container) Close(ctx context.Context) error {
	// Close tracer
	if c.tracerCleanup != nil {
		c.tracerCleanup()
	}

	// Close Dapr client
	if c.daprClient != nil {
		c.daprClient.Close()
	}

	c.logger.Info("Container closed successfully")
	return nil
}

// getEnvOrDefault returns environment variable value or default if not set
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
} 