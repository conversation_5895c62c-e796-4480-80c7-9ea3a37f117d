package container

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"strings"
	"sync"

	"github.com/gin-gonic/gin"
	daprclient "github.com/dapr/go-sdk/client"
	"go.opentelemetry.io/otel/trace"

	"webhook_microservice/internal/app"
	"webhook_microservice/internal/config"
	"webhook_microservice/internal/infra/clickhouse"
	"webhook_microservice/internal/infra/dapr"
	"webhook_microservice/internal/infra/messaging"
	"webhook_microservice/internal/infra/mongodb"
	"webhook_microservice/internal/logging"
	"webhook_microservice/internal/observability"
)

// Config represents the container's simplified configuration
type Config struct {
	// Basic settings
	HTTPPort, GinMode, Environment string
	LogLevel                       slog.Level

	// Webhook microservice doesn't need traditional databases

	// Dapr settings
	DaprEnabled                                                 bool
	DaprAppID, DaprPubSubName, DaprStateStore, DaprSecretStore string

	// Observability settings
	PrometheusEnabled, TracingEnabled bool
	JaegerEndpoint                    string
}

// Container holds all the application dependencies
type Container struct {
	// Configuration embedded directly
	config Config

	// Core infrastructure
	logger *slog.Logger
	tracer trace.Tracer

	// Webhook microservice uses only Dapr for state and messaging

	// Dapr clients (individual fields)
	daprClient       daprclient.Client
	stateClient      *dapr.StateClient
	pubsubClient     *dapr.PubSubClient
	invocationClient *dapr.InvocationClient
	secretsClient    *dapr.SecretsClient

	// Webhook services will be created on-demand in handlers

	// Initialization control
	initOnce sync.Once
	err      error

	// Cleanup functions
	tracerCleanup func()
}

// New creates a new container with all dependencies wired together
func New() (*Container, error) {
	c := &Container{}
	c.loadConfig()
	c.initLogger()

	c.initOnce.Do(func() {
		c.initTracer()
		c.initDaprClients()
	})

	if c.err != nil {
		c.logger.Error("Container initialization failed", "error", c.err)
		return nil, c.err
	}

	c.logger.Info("Container initialized successfully")
	return c, nil
}

// loadConfig loads configuration from environment variables
func (c *Container) loadConfig() {
	// Environment-first configuration loading
	c.config.HTTPPort = getEnvOrDefault("HTTP_PORT", "8080")
	c.config.GinMode = getEnvOrDefault("GIN_MODE", gin.DebugMode)
	c.config.Environment = getEnvOrDefault("ENVIRONMENT", "development")

	// Parse log level
	logLevel := getEnvOrDefault("LOG_LEVEL", "info")
	switch strings.ToLower(logLevel) {
	case "debug":
		c.config.LogLevel = slog.LevelDebug
	case "warn":
		c.config.LogLevel = slog.LevelWarn
	case "error":
		c.config.LogLevel = slog.LevelError
	default:
		c.config.LogLevel = slog.LevelInfo
	}

	// Webhook microservice doesn't need traditional databases

	// Dapr settings
	c.config.DaprEnabled = getEnvOrDefault("DAPR_ENABLED", "true") == "true"
	c.config.DaprAppID = getEnvOrDefault("DAPR_APP_ID", "webhook-microservice")
	c.config.DaprPubSubName = getEnvOrDefault("DAPR_PUBSUB_NAME", "redis-pubsub")
	c.config.DaprStateStore = getEnvOrDefault("DAPR_STATE_STORE", "statestore")
	c.config.DaprSecretStore = getEnvOrDefault("DAPR_SECRET_STORE", "config-secret-store")

	// Observability settings
	c.config.PrometheusEnabled = getEnvOrDefault("PROMETHEUS_ENABLED", "true") == "true"
	c.config.TracingEnabled = getEnvOrDefault("TRACING_ENABLED", "false") == "true"
	c.config.JaegerEndpoint = getEnvOrDefault("JAEGER_ENDPOINT", "")
}

// initLogger initializes the structured logger
func (c *Container) initLogger() {
	opts := &slog.HandlerOptions{
		Level:     c.config.LogLevel,
		AddSource: c.config.GinMode == gin.DebugMode,
	}
	c.logger = slog.New(slog.NewJSONHandler(os.Stdout, opts)).With("service", "webhook-microservice")
	slog.SetDefault(c.logger)
}

// initTracer initializes OpenTelemetry tracing if enabled
func (c *Container) initTracer() {
	if c.err != nil {
		return
	}

	if !c.config.TracingEnabled || c.config.JaegerEndpoint == "" {
		c.logger.Info("Tracing disabled or no Jaeger endpoint configured")
		return
	}

	cleanup, err := observability.InitTracer("webhook-microservice", c.config.JaegerEndpoint)
	if err != nil {
		c.logger.Warn("Failed to initialize tracer, continuing without tracing", "error", err)
		return
	}

	c.tracer = observability.GetTracer("webhook-microservice")
	c.tracerCleanup = cleanup
	c.logger.Info("Tracer initialized successfully")
}

// initDatabaseClients initializes database clients with graceful degradation
func (c *Container) initDatabaseClients() {
	if c.err != nil {
		return
	}

	// Initialize ClickHouse
	if c.config.ClickHouseDSN != "" {
		chClient, err := clickhouse.NewClient(c.config.ClickHouseDSN)
		if err != nil {
			c.logger.Warn("Failed to initialize ClickHouse client", "error", err)
		} else {
			c.clickhouseClient = chClient
			c.logger.Info("ClickHouse client initialized successfully")
		}
	}

	// Initialize MongoDB
	if c.config.MongoDBURI != "" {
		mongoClient, err := mongodb.NewClient(context.Background(), c.config.MongoDBURI)
		if err != nil {
			c.logger.Warn("Failed to initialize MongoDB client", "error", err)
		} else {
			c.mongoClient = mongoClient
			c.logger.Info("MongoDB client initialized successfully")
		}
	}
}

// initDaprClients initializes Dapr clients with graceful degradation
func (c *Container) initDaprClients() {
	if c.err != nil {
		return
	}

	if !c.config.DaprEnabled {
		c.logger.Info("Dapr disabled in configuration")
		return
	}

	var err error
	c.daprClient, err = daprclient.NewClient()
	if err != nil {
		c.logger.Warn("Failed to initialize Dapr client, continuing without Dapr", "error", err)
		return
	}

	// Create a zap logger for Dapr clients (they expect zap.Logger)
	zapLogger, err := logging.NewLogger(strings.ToLower(c.config.LogLevel.String()))
	if err != nil {
		c.logger.Warn("Failed to create zap logger for Dapr clients", "error", err)
		return
	}

	// Initialize individual Dapr clients
	c.stateClient = dapr.NewStateClient(c.daprClient)
	c.pubsubClient = dapr.NewPubSubClient(c.daprClient, zapLogger.WithGroup("PubSub").Logger)
	c.invocationClient = dapr.NewInvocationClient(c.daprClient, zapLogger.WithGroup("Invocation").Logger)
	
	// Create a temporary config for secrets client
	tempConfig := &config.Config{}
	tempConfig.Dapr.SecretStore = c.config.DaprSecretStore
	c.secretsClient = dapr.NewSecretsClient(c.daprClient, tempConfig, zapLogger.WithGroup("Secrets").Logger)

	c.logger.Info("Dapr clients initialized successfully")
}

// initRepositories initializes repositories if database clients are available
func (c *Container) initRepositories() {
	if c.err != nil {
		return
	}

	// Initialize ClickHouse repository if client is available
	if c.clickhouseClient != nil {
		repo, err := clickhouse.NewTemplateRepository(c.clickhouseClient.DB())
		if err != nil {
			c.logger.Warn("Failed to create ClickHouse repository", "error", err)
		} else {
			c.templateRepo = repo
			c.logger.Info("ClickHouse repository initialized", "component", "TemplateRepo")
		}
	}

	// Initialize MongoDB repository if client is available
	if c.mongoClient != nil {
		mongoRepo, err := mongodb.NewTemplateRepository(c.mongoClient.Database("templates"))
		if err != nil {
			c.logger.Warn("Failed to create MongoDB repository", "error", err)
		} else {
			c.mongoTemplateRepo = mongoRepo
			c.logger.Info("MongoDB repository initialized", "component", "MongoTemplateRepo")
		}
	}
}

// initServices initializes application services
func (c *Container) initServices() {
	if c.err != nil {
		return
	}

	// Initialize template service if repositories are available
	if c.templateRepo != nil || c.mongoTemplateRepo != nil {
		c.templateService = app.NewTemplateService(
			c.templateRepo,
			c.mongoTemplateRepo,
			c.eventPublisher,
			c.logger.WithGroup("TemplateService"),
		)
		c.logger.Info("Template service initialized")
	}
}

// initEventPublisher initializes event publisher if Dapr pub/sub is available
func (c *Container) initEventPublisher() {
	if c.err != nil {
		return
	}

	if c.daprClient != nil && c.config.DaprPubSubName != "" {
		publisher, err := messaging.NewDaprEventPublisher(
			c.daprClient,
			c.config.DaprPubSubName,
			c.config.DaprAppID,
			c.logger.WithGroup("EventPublisher"),
		)
		if err != nil {
			c.logger.Warn("Failed to create event publisher", "error", err)
		} else {
			c.eventPublisher = publisher
			c.logger.Info("Event publisher initialized successfully")
		}
	} else {
		c.logger.Info("Event publisher not configured (Dapr pub/sub not available)")
	}
}

// Accessor methods
func (c *Container) Logger() *slog.Logger                           { return c.logger }
func (c *Container) Config() Config                                 { return c.config }
func (c *Container) TemplateService() *app.TemplateService          { return c.templateService }
func (c *Container) DaprClient() daprclient.Client                  { return c.daprClient }
func (c *Container) StateClient() *dapr.StateClient                 { return c.stateClient }
func (c *Container) PubSubClient() *dapr.PubSubClient               { return c.pubsubClient }
func (c *Container) InvocationClient() *dapr.InvocationClient       { return c.invocationClient }
func (c *Container) SecretsClient() *dapr.SecretsClient             { return c.secretsClient }
func (c *Container) EventPublisher() app.EventPublisher             { return c.eventPublisher }

// HealthCheck performs a health check on all available components
func (c *Container) HealthCheck(ctx context.Context) error {
	var errors []error

	// Check ClickHouse
	if c.clickhouseClient != nil {
		if err := c.clickhouseClient.DB().PingContext(ctx); err != nil {
			errors = append(errors, fmt.Errorf("ClickHouse health check failed: %w", err))
		}
	}

	// Check MongoDB - we need to add a Ping method to the mongodb.Client or access the underlying client
	if c.mongoClient != nil {
		// For now, we'll just check if we can get a database (basic connectivity test)
		db := c.mongoClient.Database("health-check")
		if db == nil {
			errors = append(errors, fmt.Errorf("MongoDB health check failed: unable to get database"))
		}
	}

	// Check Dapr
	if c.daprClient != nil && c.stateClient != nil {
		if _, err := c.stateClient.GetState(ctx, c.config.DaprStateStore, "health-check"); err != nil {
			// This is expected if the key doesn't exist, but it means Dapr is responding
			c.logger.Debug("Dapr health check completed", "error", err)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("health check failures: %v", errors)
	}

	return nil
}

// Close closes all connections and resources
func (c *Container) Close(ctx context.Context) error {
	var errors []error

	// Close tracer
	if c.tracerCleanup != nil {
		c.tracerCleanup()
	}

	// Close ClickHouse connection
	if c.clickhouseClient != nil {
		if err := c.clickhouseClient.Close(); err != nil {
			errors = append(errors, fmt.Errorf("failed to close ClickHouse connection: %w", err))
		}
	}

	// Close MongoDB connection
	if c.mongoClient != nil {
		if err := c.mongoClient.Close(ctx); err != nil {
			errors = append(errors, fmt.Errorf("failed to close MongoDB connection: %w", err))
		}
	}

	// Close Dapr client
	if c.daprClient != nil {
		c.daprClient.Close()
	}

	if len(errors) > 0 {
		return fmt.Errorf("errors during close: %v", errors)
	}

	c.logger.Info("Container closed successfully")
	return nil
}

// getEnvOrDefault returns environment variable value or default if not set
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
} 