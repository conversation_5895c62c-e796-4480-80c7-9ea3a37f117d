package container

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"strings"
	"sync"

	daprclient "github.com/dapr/go-sdk/client"
	"go.opentelemetry.io/otel/trace"

	"webhook_microservice/internal/config"
	"webhook_microservice/internal/infra/tracing"
)

// Container configuration is now handled by the config package

// Container holds all the application dependencies
type Container struct {
	// Configuration from config package
	config *config.Config

	// Core infrastructure
	logger *slog.Logger
	tracer trace.Tracer

	// Simple Dapr client for webhook functionality
	daprClient daprclient.Client

	// Initialization control
	initOnce sync.Once
	err      error

	// Cleanup functions
	tracerCleanup func()
}

// NewContainer creates a new container with all dependencies wired together
func NewContainer() *Container {
	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		// Log error but continue with defaults (handled by LoadConfig)
		slog.Error("Failed to load configuration, using defaults", "error", err)
		// LoadConfig already returns defaults on error, so we can still use cfg
		if cfg == nil {
			panic("Failed to load configuration and no defaults available")
		}
	}

	c := &Container{
		config: cfg,
	}

	c.initLogger()
	c.initTracer()
	c.initDaprClient()

	return c
}

// initLogger initializes the structured logger
func (c *Container) initLogger() {
	// Use config first, then environment variable, then default
	logLevel := c.config.Logging.Level
	if logLevel == "" {
		logLevel = getEnvOrDefault("LOG_LEVEL", "info")
	}

	var level slog.Level
	switch strings.ToLower(logLevel) {
	case "debug":
		level = slog.LevelDebug
	case "warn":
		level = slog.LevelWarn
	case "error":
		level = slog.LevelError
	default:
		level = slog.LevelInfo
	}

	opts := &slog.HandlerOptions{
		Level:     level,
		AddSource: c.config.Environment == "development",
	}
	c.logger = slog.New(slog.NewJSONHandler(os.Stdout, opts)).With("service", "webhook-microservice")
	slog.SetDefault(c.logger)
}

// initTracer initializes OpenTelemetry tracing if enabled
func (c *Container) initTracer() {
	// Use config first, then environment variables
	tracingEnabled := c.config.Observability.Tracing.Enabled
	jaegerEndpoint := c.config.Observability.Tracing.Jaeger

	// Environment variable overrides
	if envEnabled := getEnvOrDefault("TRACING_ENABLED", ""); envEnabled != "" {
		tracingEnabled = envEnabled == "true"
	}
	if envEndpoint := getEnvOrDefault("JAEGER_ENDPOINT", ""); envEndpoint != "" {
		jaegerEndpoint = envEndpoint
	}

	if !tracingEnabled || jaegerEndpoint == "" {
		c.logger.Info("Tracing disabled or no Jaeger endpoint configured")
		return
	}

	tracingConfig := tracing.Config{
		ServiceName:    c.config.Dapr.AppID,
		JaegerEndpoint: jaegerEndpoint,
		Enabled:        tracingEnabled,
	}

	cleanup, err := tracing.Initialize(tracingConfig)
	if err != nil {
		c.logger.Warn("Failed to initialize tracer, continuing without tracing", "error", err)
		return
	}

	c.tracerCleanup = cleanup
	c.logger.Info("Tracer initialized successfully")
}

// initDaprClient initializes a simple Dapr client
func (c *Container) initDaprClient() {
	// Use config first, then environment variable
	daprEnabled := c.config.Dapr.Enabled
	if envEnabled := getEnvOrDefault("DAPR_ENABLED", ""); envEnabled != "" {
		daprEnabled = envEnabled == "true"
	}

	if !daprEnabled {
		c.logger.Info("Dapr disabled in configuration")
		return
	}

	var err error
	c.daprClient, err = daprclient.NewClient()
	if err != nil {
		c.logger.Warn("Failed to initialize Dapr client, continuing without Dapr", "error", err)
		return
	}

	c.logger.Info("Dapr client initialized successfully")
}

// Accessor methods
func (c *Container) Logger() *slog.Logger       { return c.logger }
func (c *Container) Config() *config.Config     { return c.config }
func (c *Container) DaprClient() daprclient.Client { return c.daprClient }

// HealthCheck performs a health check on all available components
func (c *Container) HealthCheck(ctx context.Context) error {
	// Simple health check - just verify the container is initialized
	if c.logger == nil {
		return fmt.Errorf("logger not initialized")
	}

	// Check Dapr if available
	if c.daprClient != nil {
		// Simple Dapr health check by trying to get state
		_, err := c.daprClient.GetState(ctx, c.config.StateStoreName(), "health-check", nil)
		if err != nil {
			c.logger.Debug("Dapr health check completed", "error", err)
		}
	}

	return nil
}

// Close closes all connections and resources
func (c *Container) Close(ctx context.Context) error {
	// Close tracer
	if c.tracerCleanup != nil {
		c.tracerCleanup()
	}

	// Close Dapr client
	if c.daprClient != nil {
		c.daprClient.Close()
	}

	c.logger.Info("Container closed successfully")
	return nil
}

// getEnvOrDefault returns environment variable value or default if not set
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
} 