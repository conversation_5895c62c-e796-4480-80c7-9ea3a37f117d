package domain

import "errors"

// Domain errors for webhook processing
var (
	// DeliveryID related errors
	ErrInvalidDeliveryID  = errors.New("invalid delivery ID")
	ErrDeliveryIDTooLong  = errors.New("delivery ID too long")
	
	// Source related errors
	ErrInvalidSource = errors.New("invalid source")
	
	// Payload related errors
	ErrEmptyPayload = errors.New("empty payload")
	
	// Timestamp related errors
	ErrInvalidReceivedAt = errors.New("invalid received at timestamp")
	
	// Webhook event related errors
	ErrInvalidWebhookEvent = errors.New("invalid webhook event")
)
