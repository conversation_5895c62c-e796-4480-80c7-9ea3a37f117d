package domain

import (
	"fmt"
	"strings"
	"time"
)

// WebhookEvent represents an HTTP payload received from a third-party system
type WebhookEvent struct {
	DeliveryID DeliveryID `json:"delivery_id"`
	Source     string     `json:"source"`
	Payload    []byte     `json:"payload"`
	ReceivedAt time.Time  `json:"received_at"`
}

// DeliveryID is a unique identifier for a webhook event
type DeliveryID struct {
	ID string `json:"id"`
}

// ReceivedAt represents when a webhook event was received
type ReceivedAt struct {
	Time time.Time `json:"time"`
}

// NewDeliveryID creates a new DeliveryID with validation
func NewDeliveryID(raw string) (DeliveryID, error) {
	if raw == "" {
		return DeliveryID{}, ErrInvalidDeliveryID
	}

	// Trim whitespace and validate length
	id := strings.TrimSpace(raw)
	if len(id) == 0 {
		return DeliveryID{}, ErrInvalidDeliveryID
	}

	// Basic validation - delivery IDs should be reasonable length
	if len(id) > 255 {
		return DeliveryID{}, ErrDeliveryIDTooLong
	}

	return DeliveryID{ID: id}, nil
}

// String returns the string representation of the DeliveryID
func (d DeliveryID) String() string {
	return d.ID
}

// IsEmpty checks if the DeliveryID is empty
func (d DeliveryID) IsEmpty() bool {
	return d.ID == ""
}

// NewReceivedAt creates a new ReceivedAt with current UTC time
func NewReceivedAt() ReceivedAt {
	return ReceivedAt{Time: time.Now().UTC()}
}

// NewWebhookEvent creates a new WebhookEvent with validation
func NewWebhookEvent(deliveryID DeliveryID, source string, payload []byte) (*WebhookEvent, error) {
	if deliveryID.IsEmpty() {
		return nil, ErrInvalidDeliveryID
	}

	if source == "" {
		return nil, ErrInvalidSource
	}

	if len(payload) == 0 {
		return nil, ErrEmptyPayload
	}

	// Validate source format (basic validation)
	source = strings.TrimSpace(source)
	if len(source) == 0 {
		return nil, ErrInvalidSource
	}

	return &WebhookEvent{
		DeliveryID: deliveryID,
		Source:     source,
		Payload:    payload,
		ReceivedAt: NewReceivedAt().Time,
	}, nil
}

// Validate validates the webhook event invariants
func (w *WebhookEvent) Validate() error {
	if w.DeliveryID.IsEmpty() {
		return ErrInvalidDeliveryID
	}

	if w.Source == "" {
		return ErrInvalidSource
	}

	if len(w.Payload) == 0 {
		return ErrEmptyPayload
	}

	if w.ReceivedAt.IsZero() {
		return ErrInvalidReceivedAt
	}

	return nil
}

// GetKey returns a unique key for this webhook event (for deduplication)
func (w *WebhookEvent) GetKey() string {
	return fmt.Sprintf("%s:%s", w.Source, w.DeliveryID.String())
}