package domain

import (
	"time"

	"github.com/google/uuid"
)

// Template represents a template entity in our domain
type Template struct {
	ID        string    `json:"id" bson:"id"`
	Name      string    `json:"name" bson:"name"`
	Content   string    `json:"content" bson:"content"`
	CreatedAt time.Time `json:"created_at" bson:"created_at"`
	UpdatedAt time.Time `json:"updated_at" bson:"updated_at"`
}

// NewTemplate creates a new Template instance
func NewTemplate(name, content string) *Template {
	now := time.Now()
	return &Template{
		ID:        uuid.New().String(),
		Name:      name,
		Content:   content,
		CreatedAt: now,
		UpdatedAt: now,
	}
}

// Update modifies the template's content and updates the UpdatedAt timestamp
func (t *Template) Update(content string) {
	t.Content = content
	t.UpdatedAt = time.Now()
} 