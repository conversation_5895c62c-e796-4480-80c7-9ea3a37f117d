#!/usr/bin/env bash
set -euo pipefail

# Determine project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_ROOT"

echo "🚀 Starting Webhook Microservice..."

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Ensure Go modules
echo "📦 Installing dependencies..."
go mod tidy

# Start infrastructure services
echo "🐳 Starting infrastructure services..."
docker-compose up -d redis jaeger otel-collector
sleep 5  # Wait for services to start

# Wait for Redis to be ready
echo "⏳ Waiting for Redis..."
REDIS_READY=false
for i in {1..30}; do
    if docker exec webhook-microservice-redis redis-cli ping >/dev/null 2>&1; then
        REDIS_READY=true
        break
    fi
    echo "Redis is not ready yet... (attempt $i/30)"
    sleep 2
done

if [ "$REDIS_READY" = false ]; then
    echo "❌ Redis failed to start after 60 seconds"
    echo "🔍 Checking Redis container status..."
    docker ps -a | grep webhook-microservice-redis
    docker logs webhook-microservice-redis
    exit 1
fi
echo "✅ Redis is ready!"

echo "🔧 Setting up Dapr..."

# Install Dapr CLI if missing
if ! command -v dapr &>/dev/null; then
  echo "📥 Installing Dapr CLI..."
  OS_NAME="$(uname -s)"
  if [ "$OS_NAME" = "Linux" ]; then
    wget -q https://raw.githubusercontent.com/dapr/cli/master/install/install.sh -O - | bash
  elif [ "$OS_NAME" = "Darwin" ]; then
    brew install dapr/tap/dapr-cli
  else
    echo "❌ Unsupported OS. Please install Dapr manually from https://docs.dapr.io/getting-started/install-dapr-cli/"
    exit 1
  fi
  echo "🔧 Initializing Dapr runtime..."
  dapr init --wait
  echo "✅ Dapr installed and initialized successfully."
fi

# Cleanup on exit
cleanup() {
  echo "🛑 Shutting down..."
  kill "$DAPR_PID" 2>/dev/null || true
  docker-compose down
  echo "✅ Cleanup complete"
}
trap cleanup SIGINT SIGTERM

# Run service with Dapr sidecar
echo "🚀 Starting webhook service with Dapr sidecar..."
dapr run \
  --app-id webhook-microservice \
  --app-port 8080 \
  --dapr-http-port 3500 \
  --metrics-port 9090 \
  --components-path ./components \
  -- go run ./cmd/server/main.go &

DAPR_PID=$!

echo "✅ Webhook microservice is running!"
echo "📡 Endpoints:"
echo "  - Webhook: http://localhost:8080/webhook"
echo "  - Health: http://localhost:8080/healthz"
echo "  - Metrics: http://localhost:8080/metrics"
echo "  - Dapr HTTP: http://localhost:3500"
echo ""
echo "📝 Test webhook:"
echo "  curl -X POST http://localhost:8080/webhook \\"
echo "    -H 'Content-Type: application/json' \\"
echo "    -H 'X-Delivery-ID: test-123' \\"
echo "    -H 'X-Source: github.com' \\"
echo "    -d '{\"test\": \"data\"}'"
echo ""
echo "Press Ctrl+C to stop..."

wait "$DAPR_PID"