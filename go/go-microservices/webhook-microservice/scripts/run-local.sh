#!/bin/bash
set -e

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Change to project root directory
cd "$PROJECT_ROOT"

# Install all dependencies
echo "Installing all dependencies..."
go mod tidy

# Start services
echo "Starting services..."
docker-compose up -d

# Wait for ClickHouse to be ready
echo "Waiting for ClickHouse..."
until docker exec clickhouse clickhouse-client --query "SELECT 1" >/dev/null 2>&1; do
    echo "ClickHouse is not ready yet..."
    sleep 1
done
echo "ClickHouse is ready!"

# Wait for MongoDB to be ready
echo "Waiting for MongoDB..."
until docker exec mongodb mongosh --eval "db.adminCommand('ping')" >/dev/null 2>&1; do
    echo "MongoDB is not ready yet..."
    sleep 1
done
echo "MongoDB is ready!"

# Wait for Redis to be ready
echo "Waiting for Redis..."
until docker exec redis redis-cli ping >/dev/null 2>&1; do
    echo "Redis is not ready yet..."
    sleep 1
done
echo "Redis is ready!"

echo "Services are up. Running migrations..."

# Run ClickHouse migration
echo "Running ClickHouse migration..."
docker exec clickhouse clickhouse-client --multiquery < migrations/0001_create_tables.sql

echo "Migration complete. Starting Dapr sidecar..."

# Function to check and install Dapr if needed
install_dapr_if_needed() {
    if ! command -v dapr &> /dev/null; then
        echo "Dapr CLI not found. Installing..."
        
        # Detect OS
        OS_NAME=$(uname -s)
        if [ "$OS_NAME" = "Linux" ]; then
            wget -q https://raw.githubusercontent.com/dapr/cli/master/install/install.sh -O - | /bin/bash
        elif [ "$OS_NAME" = "Darwin" ]; then
            brew install dapr/tap/dapr-cli
        else
            echo "Unsupported OS. Please install Dapr manually from https://docs.dapr.io/getting-started/install-dapr-cli/"
            exit 1
        fi
        
        # Initialize Dapr
        echo "Initializing Dapr runtime..."
        dapr init
        echo "Dapr installed and initialized successfully."
    fi
}

# Call this function before starting Dapr
install_dapr_if_needed

# Function to cleanup on exit
cleanup() {
    echo "Shutting down..."
    if [ ! -z "$DAPR_PID" ]; then
        echo "Stopping Dapr sidecar..."
        kill $DAPR_PID 2>/dev/null || true
        wait $DAPR_PID 2>/dev/null || true
    fi
    echo "Stopping Docker services..."
    docker-compose down
    exit 0
}

# Set trap for cleanup
trap cleanup SIGINT SIGTERM

# Start Dapr sidecar in background
echo "Starting Dapr sidecar..."
dapr run \
  --app-id template-microservice \
  --app-port 8080 \
  --dapr-http-port 3500 \
  --dapr-grpc-port 50001 \
  --resources-path ./components \
  --log-level info \
  --app-protocol http \
  -- go run ./cmd/server/main.go &

# Store the PID for cleanup
DAPR_PID=$!

echo "Dapr sidecar started with PID: $DAPR_PID"
echo "Application is running..."
echo "- Application: http://localhost:8080"
echo "- Dapr HTTP: http://localhost:3500"
echo "- Health check: http://localhost:8080/healthz"
echo "- Metrics: http://localhost:8080/metrics"
echo ""
echo "Press Ctrl+C to stop..."

# Wait for the process
wait $DAPR_PID