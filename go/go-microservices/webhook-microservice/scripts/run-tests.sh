#!/bin/bash

# Test runner script for webhook microservice
# Supports both unit tests and integration tests

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
TEST_TIMEOUT="60s"
SERVICE_PORT="8080"
DAPR_PORT="3500"

# Function to print colored output
print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if service is running
is_service_running() {
    curl -s -f "http://localhost:${SERVICE_PORT}/healthz" > /dev/null 2>&1
}

# Function to check if Dapr is running
is_dapr_running() {
    curl -s -f "http://localhost:${DAPR_PORT}/v1.0/healthz" > /dev/null 2>&1
}

# Function to run unit tests
run_unit_tests() {
    print_status "Running unit tests..."
    cd "$PROJECT_DIR"
    
    # Set test environment
    export GIN_MODE=test
    export WEBHOOK_PORT="$SERVICE_PORT"
    export WEBHOOK_DAPR_HTTP_PORT="$DAPR_PORT"
    
    # Run unit tests only
    go test -v -timeout="$TEST_TIMEOUT" -run=".*_Unit" ./tests/
    
    if [ $? -eq 0 ]; then
        print_success "Unit tests passed!"
        return 0
    else
        print_error "Unit tests failed!"
        return 1
    fi
}

# Function to run integration tests
run_integration_tests() {
    print_status "Running integration tests..."
    
    # Check if service is running
    if ! is_service_running; then
        print_error "Webhook service is not running on port $SERVICE_PORT"
        print_status "Please start the service first with: bash scripts/run-local.sh"
        return 1
    fi
    
    # Check if Dapr is running
    if ! is_dapr_running; then
        print_warning "Dapr is not running on port $DAPR_PORT"
        print_status "Some integration tests may fail"
    else
        print_success "Dapr is running and healthy"
    fi
    
    cd "$PROJECT_DIR"
    
    # Run integration tests only
    go test -v -timeout="$TEST_TIMEOUT" -run=".*_Integration" ./tests/
    
    if [ $? -eq 0 ]; then
        print_success "Integration tests passed!"
        return 0
    else
        print_error "Integration tests failed!"
        return 1
    fi
}

# Function to run all tests
run_all_tests() {
    print_status "Running all tests..."
    
    local unit_result=0
    local integration_result=0
    
    # Run unit tests
    run_unit_tests
    unit_result=$?
    
    echo ""
    
    # Run integration tests
    run_integration_tests
    integration_result=$?
    
    echo ""
    print_status "Test Summary:"
    
    if [ $unit_result -eq 0 ]; then
        print_success "Unit tests: PASSED"
    else
        print_error "Unit tests: FAILED"
    fi
    
    if [ $integration_result -eq 0 ]; then
        print_success "Integration tests: PASSED"
    else
        print_error "Integration tests: FAILED"
    fi
    
    if [ $unit_result -eq 0 ] && [ $integration_result -eq 0 ]; then
        print_success "All tests passed! 🎉"
        return 0
    else
        print_error "Some tests failed!"
        return 1
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  unit         Run unit tests only"
    echo "  integration  Run integration tests only (requires running service)"
    echo "  all          Run all tests (default)"
    echo "  help         Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Run all tests"
    echo "  $0 unit              # Run unit tests only"
    echo "  $0 integration       # Run integration tests only"
    echo ""
    echo "Prerequisites for integration tests:"
    echo "  - Start the service: bash scripts/run-local.sh"
    echo "  - Service should be running on port $SERVICE_PORT"
    echo "  - Dapr should be running on port $DAPR_PORT"
}

# Main script logic
main() {
    case "${1:-all}" in
        "unit")
            run_unit_tests
            ;;
        "integration")
            run_integration_tests
            ;;
        "all")
            run_all_tests
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            print_error "Unknown option: $1"
            echo ""
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
