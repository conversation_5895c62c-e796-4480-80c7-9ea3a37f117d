#!/usr/bin/env bash
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
WEBHOOK_URL="http://localhost:8080"
DAPR_URL="http://localhost:3500"
JAEGER_URL="http://localhost:16686"

echo -e "${BLUE}🧪 Testing Webhook Microservice - DDD Specification Compliance${NC}"
echo "=================================================================="

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0

# Helper functions
pass_test() {
    echo -e "${GREEN}✅ PASS:${NC} $1"
    ((TESTS_PASSED++))
}

fail_test() {
    echo -e "${RED}❌ FAIL:${NC} $1"
    ((TESTS_FAILED++))
}

warn_test() {
    echo -e "${YELLOW}⚠️  WARN:${NC} $1"
}

info_test() {
    echo -e "${BLUE}ℹ️  INFO:${NC} $1"
}

# Test function
test_endpoint() {
    local method=$1
    local url=$2
    local expected_status=$3
    local description=$4
    local headers=${5:-""}
    local data=${6:-""}
    
    local curl_cmd="curl -s -w '%{http_code}' -o /tmp/response"
    
    if [ -n "$headers" ]; then
        curl_cmd="$curl_cmd $headers"
    fi
    
    if [ -n "$data" ]; then
        curl_cmd="$curl_cmd -d '$data'"
    fi
    
    curl_cmd="$curl_cmd -X $method $url"
    
    local status_code
    status_code=$(eval $curl_cmd)
    
    if [ "$status_code" = "$expected_status" ]; then
        pass_test "$description (HTTP $status_code)"
        return 0
    else
        fail_test "$description (Expected: $expected_status, Got: $status_code)"
        return 1
    fi
}

echo -e "\n${BLUE}1. Infrastructure Health Checks${NC}"
echo "================================"

# Test 1.1: Webhook service health
test_endpoint "GET" "$WEBHOOK_URL/healthz" "200" "Webhook service health endpoint"

# Test 1.2: Webhook service readiness
test_endpoint "GET" "$WEBHOOK_URL/readyz" "200" "Webhook service readiness endpoint"

# Test 1.3: Dapr health
test_endpoint "GET" "$DAPR_URL/v1.0/healthz" "204" "Dapr sidecar health endpoint"

# Test 1.4: Metrics endpoint
test_endpoint "GET" "$WEBHOOK_URL/metrics" "200" "Prometheus metrics endpoint"

# Test 1.5: Jaeger UI
if curl -s --connect-timeout 5 "$JAEGER_URL" > /dev/null; then
    pass_test "Jaeger UI accessibility"
else
    fail_test "Jaeger UI accessibility"
fi

echo -e "\n${BLUE}2. Dapr Component Verification${NC}"
echo "==============================="

# Test 2.1: Dapr metadata (shows loaded components)
if curl -s "$DAPR_URL/v1.0/metadata" | jq -e '.components[] | select(.name=="statestore")' > /dev/null 2>&1; then
    pass_test "Dapr state store component loaded"
else
    fail_test "Dapr state store component not found"
fi

if curl -s "$DAPR_URL/v1.0/metadata" | jq -e '.components[] | select(.name=="redis-pubsub")' > /dev/null 2>&1; then
    pass_test "Dapr pub/sub component loaded"
else
    fail_test "Dapr pub/sub component not found"
fi

echo -e "\n${BLUE}3. WAF Middleware Testing${NC}"
echo "========================="

# Test 3.1: Missing required headers (should be blocked by WAF)
test_endpoint "POST" "$WEBHOOK_URL/webhook" "403" "WAF blocks request without required headers" "" '{"test": "data"}'

# Test 3.2: Missing delivery ID
test_endpoint "POST" "$WEBHOOK_URL/webhook" "400" "Missing X-Delivery-ID header" "-H 'Content-Type: application/json' -H 'X-Source: test'" '{"test": "data"}'

# Test 3.3: Missing source
test_endpoint "POST" "$WEBHOOK_URL/webhook" "400" "Missing X-Source header" "-H 'Content-Type: application/json' -H 'X-Delivery-ID: test-123'" '{"test": "data"}'

echo -e "\n${BLUE}4. Webhook Processing (Happy Path)${NC}"
echo "=================================="

# Test 4.1: Valid webhook request
DELIVERY_ID="test-$(date +%s)"
test_endpoint "POST" "$WEBHOOK_URL/webhook" "200" "Valid webhook processing" \
    "-H 'Content-Type: application/json' -H 'X-Delivery-ID: $DELIVERY_ID' -H 'X-Source: github.com'" \
    '{"event": "push", "repository": "test-repo"}'

echo -e "\n${BLUE}5. Deduplication Testing${NC}"
echo "========================"

# Test 5.1: Duplicate webhook (should return 200 with already_processed)
info_test "Testing deduplication with same delivery ID: $DELIVERY_ID"
RESPONSE=$(curl -s -X POST "$WEBHOOK_URL/webhook" \
    -H 'Content-Type: application/json' \
    -H "X-Delivery-ID: $DELIVERY_ID" \
    -H 'X-Source: github.com' \
    -d '{"event": "push", "repository": "test-repo"}')

if echo "$RESPONSE" | jq -e '.status == "already_processed"' > /dev/null 2>&1; then
    pass_test "Deduplication working - duplicate webhook detected"
else
    fail_test "Deduplication not working - duplicate not detected"
fi

echo -e "\n${BLUE}6. State Store Verification${NC}"
echo "==========================="

# Test 6.1: Check if state was stored in Dapr
STATE_KEY="webhook:processed:$DELIVERY_ID"
if curl -s "$DAPR_URL/v1.0/state/statestore/$STATE_KEY" | jq -r . > /dev/null 2>&1; then
    pass_test "Webhook state stored in Dapr state store"
else
    fail_test "Webhook state not found in Dapr state store"
fi

echo -e "\n${BLUE}7. Error Handling Testing${NC}"
echo "========================="

# Test 7.1: Invalid JSON payload
test_endpoint "POST" "$WEBHOOK_URL/webhook" "400" "Invalid JSON payload handling" \
    "-H 'Content-Type: application/json' -H 'X-Delivery-ID: invalid-json-test' -H 'X-Source: test'" \
    'invalid-json{'

# Test 7.2: Empty payload
test_endpoint "POST" "$WEBHOOK_URL/webhook" "400" "Empty payload handling" \
    "-H 'Content-Type: application/json' -H 'X-Delivery-ID: empty-test' -H 'X-Source: test'" \
    ''

# Test 7.3: Invalid delivery ID (too long)
LONG_ID=$(printf 'a%.0s' {1..300})
test_endpoint "POST" "$WEBHOOK_URL/webhook" "400" "Invalid delivery ID (too long)" \
    "-H 'Content-Type: application/json' -H 'X-Delivery-ID: $LONG_ID' -H 'X-Source: test'" \
    '{"test": "data"}'

echo -e "\n${BLUE}8. Rate Limiting Testing${NC}"
echo "========================"

info_test "Testing rate limiting (this may take a moment...)"
RATE_LIMIT_PASSED=true

# Send multiple requests quickly to test rate limiting
for i in {1..10}; do
    STATUS=$(curl -s -w '%{http_code}' -o /dev/null -X POST "$WEBHOOK_URL/webhook" \
        -H 'Content-Type: application/json' \
        -H "X-Delivery-ID: rate-test-$i" \
        -H 'X-Source: test' \
        -d '{"test": "data"}')
    
    if [ "$STATUS" = "429" ]; then
        pass_test "Rate limiting active (HTTP 429 received)"
        RATE_LIMIT_PASSED=true
        break
    fi
done

if [ "$RATE_LIMIT_PASSED" != "true" ]; then
    warn_test "Rate limiting not triggered (may need higher load)"
fi

echo -e "\n${BLUE}9. Observability Verification${NC}"
echo "=============================="

# Test 9.1: Metrics contain webhook-specific metrics
if curl -s "$WEBHOOK_URL/metrics" | grep -q "http_requests_total"; then
    pass_test "HTTP request metrics available"
else
    fail_test "HTTP request metrics not found"
fi

# Test 9.2: Check if traces are being generated (basic check)
info_test "Tracing verification requires manual check in Jaeger UI at $JAEGER_URL"

echo -e "\n${BLUE}📊 Test Results Summary${NC}"
echo "======================="
echo -e "Tests Passed: ${GREEN}$TESTS_PASSED${NC}"
echo -e "Tests Failed: ${RED}$TESTS_FAILED${NC}"
echo -e "Total Tests: $((TESTS_PASSED + TESTS_FAILED))"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All tests passed! Webhook microservice is working correctly.${NC}"
    exit 0
else
    echo -e "\n${RED}❌ Some tests failed. Please check the issues above.${NC}"
    exit 1
fi
