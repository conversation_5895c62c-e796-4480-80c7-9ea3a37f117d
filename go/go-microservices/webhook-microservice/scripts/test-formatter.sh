#!/bin/bash

# Test output formatter for better readability
# This script formats Go test output with colors and better structure

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
GRAY='\033[0;90m'
BOLD='\033[1m'
DIM='\033[2m'
NC='\033[0m'

# Unicode symbols
CHECK_MARK="✅"
CROSS_MARK="❌"
RUNNING="🏃"
PACKAGE="📦"
CLOCK="⏱️"

# Function to format test output
format_test_output() {
    local test_count=0
    local pass_count=0
    local fail_count=0
    local current_package=""
    
    while IFS= read -r line; do
        # Package lines
        if [[ $line == "=== RUN"* ]]; then
            test_name=$(echo "$line" | sed 's/=== RUN[[:space:]]*//')
            ((test_count++))
            echo -e "${CYAN}${RUNNING} ${BOLD}Running:${NC} ${WHITE}$test_name${NC}"

        # Pass lines
        elif [[ $line == *"--- PASS:"* ]]; then
            test_name=$(echo "$line" | sed 's/.*--- PASS: \([^[:space:]]*\).*/\1/')
            duration=$(echo "$line" | grep -o '([0-9.]*s)' || echo "")
            ((pass_count++))
            echo -e "${GREEN}${CHECK_MARK} ${BOLD}PASS:${NC} ${WHITE}$test_name${NC} ${GRAY}$duration${NC}"

        # Fail lines
        elif [[ $line == *"--- FAIL:"* ]]; then
            test_name=$(echo "$line" | sed 's/.*--- FAIL: \([^[:space:]]*\).*/\1/')
            duration=$(echo "$line" | grep -o '([0-9.]*s)' || echo "")
            ((fail_count++))
            echo -e "${RED}${CROSS_MARK} ${BOLD}FAIL:${NC} ${WHITE}$test_name${NC} ${GRAY}$duration${NC}"

        # Skip lines
        elif [[ $line == *"--- SKIP:"* ]]; then
            test_name=$(echo "$line" | sed 's/.*--- SKIP: \([^[:space:]]*\).*/\1/')
            echo -e "${YELLOW}⏭️  ${BOLD}SKIP:${NC} ${WHITE}$test_name${NC}"

        # Package result lines
        elif [[ $line == "PASS"* ]] || [[ $line == "FAIL"* ]] || [[ $line == "ok "* ]]; then
            if [[ $line == "PASS"* ]]; then
                echo -e "${GREEN}${PACKAGE} ${BOLD}Package Result:${NC} ${GREEN}PASS${NC} ${GRAY}$line${NC}"
            elif [[ $line == "FAIL"* ]]; then
                echo -e "${RED}${PACKAGE} ${BOLD}Package Result:${NC} ${RED}FAIL${NC} ${GRAY}$line${NC}"
            elif [[ $line == "ok "* ]]; then
                echo -e "${GREEN}${PACKAGE} ${BOLD}Package Result:${NC} ${GREEN}OK${NC} ${GRAY}$line${NC}"
            fi
            
        # Error details
        elif [[ $line == *"Error Trace:"* ]] || [[ $line == *"Error:"* ]] || [[ $line == *"Test:"* ]]; then
            echo -e "${RED}    ${line}${NC}"

        # Coverage lines
        elif [[ $line == *"coverage:"* ]]; then
            coverage=$(echo "$line" | grep -o '[0-9.]*%')
            if [[ $coverage == "100.0%" ]]; then
                echo -e "${GREEN}${BOLD}Coverage: $coverage${NC} ${GRAY}$line${NC}"
            elif [[ $coverage == "0.0%" ]]; then
                echo -e "${RED}${BOLD}Coverage: $coverage${NC} ${GRAY}$line${NC}"
            else
                echo -e "${YELLOW}${BOLD}Coverage: $coverage${NC} ${GRAY}$line${NC}"
            fi

        # Build/compile errors
        elif [[ $line == "#"* ]]; then
            echo -e "${PURPLE}${BOLD}Build:${NC} ${GRAY}$line${NC}"

        # Time lines
        elif [[ $line == "time="* ]]; then
            echo -e "${GRAY}$line${NC}"
            
        # Other output (usually test logs)
        else
            # Dim other output to reduce noise
            if [[ -n "$line" ]]; then
                echo -e "${DIM}$line${NC}"
            else
                echo ""
            fi
        fi
    done
    
    # Print summary if we processed any tests
    if [ $test_count -gt 0 ]; then
        echo ""
        echo -e "${BOLD}${WHITE}┌─────────────────────────────────────────┐${NC}"
        echo -e "${BOLD}${WHITE}│              TEST SUMMARY               │${NC}"
        echo -e "${BOLD}${WHITE}├─────────────────────────────────────────┤${NC}"
        echo -e "${BOLD}${WHITE}│${NC} Total Tests: ${BOLD}$test_count${NC}                      ${BOLD}${WHITE}│${NC}"
        echo -e "${BOLD}${WHITE}│${NC} ${GREEN}Passed: $pass_count${NC}                         ${BOLD}${WHITE}│${NC}"
        echo -e "${BOLD}${WHITE}│${NC} ${RED}Failed: $fail_count${NC}                         ${BOLD}${WHITE}│${NC}"
        echo -e "${BOLD}${WHITE}└─────────────────────────────────────────┘${NC}"
    fi
}

# If script is called directly, format stdin
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    format_test_output
fi
