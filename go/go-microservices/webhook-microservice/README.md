# Webhook Microservice

A high-performance webhook ingestion microservice built with Go, following Domain-Driven Design (DDD) and Clean Architecture principles, with Dapr integration for state management and event publishing.

## Overview

This microservice receives, validates, deduplicates, and publishes external webhooks to internal event streams. It's designed to handle high-throughput webhook traffic with proper observability and reliability features.

## Features

- **Webhook Ingestion**: Receives HTTP webhooks from external systems
- **Deduplication**: Prevents duplicate processing using delivery IDs
- **Event Publishing**: Publishes validated events to internal streams via Dapr
- **Observability**: Structured logging, metrics, and distributed tracing
- **Security**: WAF, rate limiting, and signature validation
- **Clean Architecture**: DDD principles with clear layer separation

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   External      │    │   Webhook       │    │   Internal      │
│   Systems       │───▶│   Microservice  │───▶│   Event Stream  │
│   (GitHub, etc) │    │                 │    │   (via Dapr)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Layer Structure

- **API Layer**: HTTP handlers, middleware (WAF, validation)
- **Application Layer**: Commands, ports, business logic
- **Domain Layer**: Core entities, value objects, business rules
- **Infrastructure Layer**: Dapr clients, external integrations

## Quick Start

### Prerequisites

- Go 1.23+
- Docker & Docker Compose
- Dapr CLI (auto-installed by script)

### Running Locally

```bash
# Start the webhook microservice
./scripts/run-local.sh
```

This will:
1. Install dependencies
2. Start Redis and observability stack
3. Install/initialize Dapr if needed
4. Launch the webhook service with Dapr sidecar

### Testing

Send a test webhook:

```bash
curl -X POST http://localhost:8080/webhook \
  -H 'Content-Type: application/json' \
  -H 'X-Delivery-ID: test-123' \
  -H 'X-Source: github.com' \
  -d '{"test": "data"}'
```

## API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/webhook` | POST | Receive webhook events |
| `/healthz` | GET | Health check |
| `/readyz` | GET | Readiness check |
| `/metrics` | GET | Prometheus metrics |

### Webhook Headers

Required headers:
- `X-Delivery-ID`: Unique identifier for deduplication
- `Content-Type`: Payload content type

Optional headers:
- `X-Source`: Source system identifier
- `X-Signature`: Webhook signature (for validation)

## Configuration

Configuration is managed via `configs/config.yaml`:

```yaml
webhook:
  topic_name: webhook-events
  rate_limit_rpm: 1000
  waf_allow_list:
    - "0.0.0.0/0"

dapr:
  app_id: webhook-microservice
  state_store: statestore
  pubsub_name: redis-pubsub
```

## Observability

- **Logs**: Structured JSON logs via slog
- **Metrics**: Prometheus metrics at `/metrics`
- **Tracing**: OpenTelemetry traces via Jaeger
- **Health**: Health checks at `/healthz` and `/readyz`

### Monitoring URLs

- Jaeger UI: http://localhost:16686
- Prometheus metrics: http://localhost:8080/metrics

## Development

### Project Structure

```
webhook-microservice/
├── cmd/server/main.go              # Application entry point
├── internal/
│   ├── api/                        # HTTP handlers & middleware
│   ├── app/                        # Application layer (commands, ports)
│   ├── domain/                     # Domain entities & value objects
│   ├── infra/dapr/                 # Dapr infrastructure adapters
│   └── container/                  # Dependency injection
├── configs/                        # Configuration files
├── components/                     # Dapr component definitions
└── scripts/run-local.sh           # Local development script
```

### Adding New Features

1. **Domain Changes**: Update entities in `internal/domain/`
2. **Application Logic**: Add commands in `internal/app/`
3. **API Changes**: Update handlers in `internal/api/`
4. **Infrastructure**: Add adapters in `internal/infra/`

## Deployment

The microservice is designed to run with Dapr in production environments. Key considerations:

- **State Store**: Configure Redis or other Dapr-compatible state store
- **Pub/Sub**: Configure message broker for event publishing
- **Observability**: Ensure OpenTelemetry collector is configured
- **Security**: Configure proper WAF rules and rate limits

## Contributing

1. Follow DDD principles and clean architecture
2. Add tests for new functionality
3. Update documentation for API changes
4. Ensure observability is maintained
