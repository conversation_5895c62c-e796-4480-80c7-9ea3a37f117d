# Domain-Driven Design (DDD) Blueprint for Webhook Microservice

All domain concepts, folder mappings, and Go types are specified here to avoid ambiguity.

## 1. Bounded Context: Webhook Ingestion

Defines terminology and models for receiving, validating, de-duplicating, and publishing external webhooks.

---

## 2. Ubiquitous Language

| Term             | Definition                                                                   |
|------------------|-------------------------------------------------------------------------------|
| **WebhookEvent** | HTTP payload received from a third-party system                               |
| **DeliveryID**   | Unique header (e.g. `X-GitHub-Delivery`) identifying one WebhookEvent         |
| **Source**       | Origin of the webhook (e.g. `"github.com"` or `"jira.mycompany.com"`)         |
| **Deduplicator** | Service that checks/saves DeliveryIDs to prevent re-processing                |
| **Publisher**    | Service that sends a WebhookEvent into the internal event stream via Dapr     |
| **ReceivedAt**   | Timestamp when the WebhookEvent arrived                                       |

---

## 3. Aggregate: WebhookEvent

**File:** `internal/domain/webhook.go`

```go
type WebhookEvent struct {
    DeliveryID DeliveryID
    Source     string
    Payload    []byte
    ReceivedAt time.Time
}
… validation invariants …
```

---

## 4. Value Objects

**File:** `internal/domain/webhook.go` (same file or separate vo file)

```go
type DeliveryID struct { ID string }

func NewDeliveryID(raw string) (DeliveryID, error) { … }

type ReceivedAt struct { Time time.Time }

func NewReceivedAt() ReceivedAt { return ReceivedAt{Time: time.Now().UTC()} }
```

---

## 5. Domain Services (Ports)

**File:** `internal/app/ports.go`

```go
type DeduplicationService interface {
    IsDuplicate(ctx context.Context, id DeliveryID) (bool, error)
    MarkProcessed(ctx context.Context, id DeliveryID) error
}

type PublishingService interface {
    Publish(ctx context.Context, event WebhookEvent) error
}
```

---

## 6. Application Command

**File:** `internal/app/commands/handle_webhook.go`

```go
type HandleWebhookCommand struct {
    DeliveryID string
    Source     string
    Payload    []byte
}

// Handler executes the command:
func HandleWebhook(ctx context.Context, cmd HandleWebhookCommand) error {
    id, err := NewDeliveryID(cmd.DeliveryID)
    if err != nil { return ErrInvalidID }
    event := WebhookEvent{
        DeliveryID: id, Source: cmd.Source,
        Payload: cmd.Payload,
        ReceivedAt: NewReceivedAt().Time,
    }
    dup, err := dedupService.IsDuplicate(ctx, id)
    if err != nil { return err }
    if dup { return ErrDuplicate }
    if err := dedupService.MarkProcessed(ctx, id); err != nil { return err }
    if err := pubService.Publish(ctx, event); err != nil { return ErrPublishFailed }
    return nil
}
```

**Errors** (`internal/app/errors.go`):
- `ErrInvalidID` → 400 Bad Request  
- `ErrDuplicate` → 200 OK (idempotent)  
- `ErrPublishFailed` → 502 Bad Gateway

---

## 7. Infrastructure Adapters

### 7.1 Dapr State Client

**File:** `internal/infra/dapr/state_client.go`

```go
type DaprStateClient struct { client dapr.Client }

func (c *DaprStateClient) IsDuplicate(ctx, id) (bool,error) { … }
func (c *DaprStateClient) MarkProcessed(ctx, id) error { … }
```

### 7.2 Dapr Pub/Sub Client

**File:** `internal/infra/dapr/pubsub.go`

```go
type DaprPubSubClient struct { client dapr.Client }

func (p *DaprPubSubClient) Publish(ctx, event) error {
    return p.client.PublishEvent(ctx,"pubsubName","topicName",event.Payload)
}
```

---

## 8. API Layer & Middleware

### 8.1 Router

**File:** `internal/api/router.go`

- Mounts middleware and handler:
  - `POST /webhook` → `webhook_handler.go`

### 8.2 WAF / Rate Limiting

**File:** `internal/api/middleware/waf.go`

- Implements IP allow-list, token bucket rate limits.

### 8.3 Validation

**File:** `internal/api/middleware/validator.go`

- Checks required headers (`X-Delivery-ID`, `X-Signature`) and JSON schema.

### 8.4 Webhook Handler

**File:** `internal/api/webhook_handler.go`

```go
func WebhookHandler(c *gin.Context) {
    cmd := parseHandleWebhookCommand(c)
    if err := commandBus.Handle(ctx, cmd); err != nil {
        mapErrorToHTTP(c, err)
        return
    }
    c.Status(http.StatusOK)
}
```

---

## 9. Configuration

**File:** `internal/config/config.go`

```go
type Config struct {
    Port           int
    PubSubName     string
    TopicName      string
    StateStoreName string
    RateLimitRPM   int
    WAFAllowList   []string
}

func LoadConfig() (*Config,error) { … }
```

- Values stored in `configs/config.yaml` or env vars.

---

## 10. Container Bootstrap

**File:** `internal/container/container.go`

- Load `Config`
- Initialize Logger & Tracer
- Create Dapr client
- Instantiate `DaprStateClient` & `DaprPubSubClient`
- Wire `DeduplicationService` & `PublishingService`
- Setup Gin router with middleware and `WebhookHandler`

---

## 11. Development Script: run-local.sh

### Purpose
Provides a reproducible local development environment:
- Starts Redis and OpenTelemetry Collector
- Ensures dependencies are ready
- Installs and initializes Dapr CLI if missing
- Launches the webhook service with its Dapr sidecar

**File:** `scripts/run-local.sh`

```bash
#!/usr/bin/env bash
set -euo pipefail

# Determine project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_ROOT"

# Ensure Go modules
go mod tidy

# Start infra
docker-compose up -d redis otel-collector
sleep 5  # or proper health checks

# Install Dapr CLI if missing
if ! command -v dapr &>/dev/null; then
  OS_NAME="$(uname -s)"
  if [ "$OS_NAME" = "Linux" ]; then
    wget -q https://raw.githubusercontent.com/dapr/cli/master/install/install.sh -O - | bash
  elif [ "$OS_NAME" = "Darwin" ]; then
    brew install dapr/tap/dapr-cli
  fi
  dapr init --wait
fi

# Cleanup on exit
cleanup() {
  kill "$DAPR_PID" 2>/dev/null || true
  docker-compose down
}
trap cleanup SIGINT SIGTERM

# Run service with Dapr sidecar
dapr run \
  --app-id webhook-service \
  --app-port 8080 \
  --dapr-http-port 3500 \
  --metrics-port 9090 \
  --components-path ./components \
  --config ./configs/config.yaml \
  -- go run ./cmd/server/main.go &

DAPR_PID=$!
wait "$DAPR_PID"
```

---

### Folder Recap
```
webhook-microservice/
├ cmd/server/main.go
├ configs/config.yaml
├ scripts/run-local.sh
├ docker-compose.yml
├ internal/
│ ├ config/config.go
│ ├ container/container.go
│ ├ api/
│ │ ├ router.go
│ │ ├ middleware/waf.go
│ │ ├ middleware/validator.go
│ │ └ webhook_handler.go
│ ├ app/
│ │ ├ commands/handle_webhook.go
│ │ ├ ports.go
│ │ └ errors.go
│ ├ domain/webhook.go
│ └ infra/dapr/
│ ├ state_client.go
│ └ pubsub.go
├ tests/
├ Taskfile.yml
├ .env.example
├ go.mod
└ README.md
```