package tests

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"webhook_microservice/internal/domain"
	"webhook_microservice/internal/infra/dapr"
)

// Mock Dapr client for testing
type MockDaprClient struct {
	mock.Mock
}

func (m *MockDaprClient) GetState(ctx context.Context, storeName, key string, meta map[string]string) ([]byte, error) {
	args := m.Called(ctx, storeName, key, meta)
	return args.Get(0).([]byte), args.Error(1)
}

func (m *MockDaprClient) SaveState(ctx context.Context, storeName, key string, data []byte, meta map[string]string) error {
	args := m.Called(ctx, storeName, key, data, meta)
	return args.Error(0)
}

func (m *MockDaprClient) PublishEvent(ctx context.Context, pubsubName, topicName string, data interface{}, meta map[string]string) error {
	args := m.Called(ctx, pubsubName, topicName, data, meta)
	return args.Error(0)
}

func (m *MockDaprClient) Close() error {
	args := m.Called()
	return args.Error(0)
}

func TestStateClient_IsProcessed(t *testing.T) {
	tests := []struct {
		name           string
		deliveryID     string
		mockResponse   []byte
		mockError      error
		expectedResult bool
		expectedError  bool
	}{
		{
			name:           "Webhook not processed (empty state)",
			deliveryID:     "test-123",
			mockResponse:   []byte{},
			mockError:      nil,
			expectedResult: false,
			expectedError:  false,
		},
		{
			name:           "Webhook not processed (nil state)",
			deliveryID:     "test-456",
			mockResponse:   nil,
			mockError:      nil,
			expectedResult: false,
			expectedError:  false,
		},
		{
			name:           "Webhook already processed",
			deliveryID:     "test-789",
			mockResponse:   []byte("processed"),
			mockError:      nil,
			expectedResult: true,
			expectedError:  false,
		},
		{
			name:           "State store error",
			deliveryID:     "test-error",
			mockResponse:   nil,
			mockError:      errors.New("state store unavailable"),
			expectedResult: false,
			expectedError:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mock
			mockClient := new(MockDaprClient)
			mockClient.On("GetState", mock.Anything, "statestore", "webhook:"+tt.deliveryID, mock.Anything).
				Return(tt.mockResponse, tt.mockError)

			// Create state client
			stateClient := dapr.NewStateClient(mockClient, "statestore")

			// Execute
			ctx := context.Background()
			result, err := stateClient.IsProcessed(ctx, tt.deliveryID)

			// Assert
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
			assert.Equal(t, tt.expectedResult, result)

			// Verify mock expectations
			mockClient.AssertExpectations(t)
		})
	}
}

func TestStateClient_MarkAsProcessed(t *testing.T) {
	tests := []struct {
		name          string
		deliveryID    string
		mockError     error
		expectedError bool
	}{
		{
			name:          "Successfully mark as processed",
			deliveryID:    "test-123",
			mockError:     nil,
			expectedError: false,
		},
		{
			name:          "State store error",
			deliveryID:    "test-error",
			mockError:     errors.New("state store unavailable"),
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mock
			mockClient := new(MockDaprClient)
			mockClient.On("SaveState", mock.Anything, "statestore", "webhook:"+tt.deliveryID, []byte("processed"), mock.Anything).
				Return(tt.mockError)

			// Create state client
			stateClient := dapr.NewStateClient(mockClient, "statestore")

			// Execute
			ctx := context.Background()
			err := stateClient.MarkAsProcessed(ctx, tt.deliveryID)

			// Assert
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// Verify mock expectations
			mockClient.AssertExpectations(t)
		})
	}
}

func TestPubSubClient_PublishWebhookEvent(t *testing.T) {
	tests := []struct {
		name          string
		event         *domain.WebhookEvent
		mockError     error
		expectedError bool
	}{
		{
			name: "Successfully publish event",
			event: func() *domain.WebhookEvent {
				event, _ := domain.NewWebhookEvent("test-123", "github.com", []byte(`{"event": "push"}`))
				return event
			}(),
			mockError:     nil,
			expectedError: false,
		},
		{
			name: "Pub/sub error",
			event: func() *domain.WebhookEvent {
				event, _ := domain.NewWebhookEvent("test-456", "gitlab.com", []byte(`{"event": "merge"}`))
				return event
			}(),
			mockError:     errors.New("pub/sub unavailable"),
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mock
			mockClient := new(MockDaprClient)
			
			expectedData := map[string]interface{}{
				"delivery_id": tt.event.DeliveryID(),
				"source":      tt.event.Source(),
				"payload":     string(tt.event.Payload()),
			}
			
			mockClient.On("PublishEvent", mock.Anything, "redis-pubsub", "webhook-events", expectedData, mock.Anything).
				Return(tt.mockError)

			// Create pub/sub client
			pubsubClient := dapr.NewPubSubClient(mockClient, "redis-pubsub", "webhook-events")

			// Execute
			ctx := context.Background()
			err := pubsubClient.PublishWebhookEvent(ctx, tt.event)

			// Assert
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// Verify mock expectations
			mockClient.AssertExpectations(t)
		})
	}
}

func TestPubSubClient_PublishWebhookEvent_NilEvent(t *testing.T) {
	// Setup mock
	mockClient := new(MockDaprClient)

	// Create pub/sub client
	pubsubClient := dapr.NewPubSubClient(mockClient, "redis-pubsub", "webhook-events")

	// Execute with nil event
	ctx := context.Background()
	err := pubsubClient.PublishWebhookEvent(ctx, nil)

	// Assert
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "webhook event cannot be nil")

	// Verify no calls were made to the mock
	mockClient.AssertNotCalled(t, "PublishEvent")
}

func TestStateClient_EdgeCases(t *testing.T) {
	t.Run("Empty delivery ID", func(t *testing.T) {
		mockClient := new(MockDaprClient)
		stateClient := dapr.NewStateClient(mockClient, "statestore")

		ctx := context.Background()
		
		// Test IsProcessed with empty delivery ID
		result, err := stateClient.IsProcessed(ctx, "")
		assert.Error(t, err)
		assert.False(t, result)
		assert.Contains(t, err.Error(), "delivery ID cannot be empty")

		// Test MarkAsProcessed with empty delivery ID
		err = stateClient.MarkAsProcessed(ctx, "")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "delivery ID cannot be empty")
	})

	t.Run("Context cancellation", func(t *testing.T) {
		mockClient := new(MockDaprClient)
		mockClient.On("GetState", mock.Anything, "statestore", "webhook:test-123", mock.Anything).
			Return(nil, context.Canceled)

		stateClient := dapr.NewStateClient(mockClient, "statestore")

		ctx, cancel := context.WithCancel(context.Background())
		cancel() // Cancel immediately

		result, err := stateClient.IsProcessed(ctx, "test-123")
		assert.Error(t, err)
		assert.False(t, result)

		mockClient.AssertExpectations(t)
	})
}

func TestPubSubClient_EdgeCases(t *testing.T) {
	t.Run("Context cancellation", func(t *testing.T) {
		mockClient := new(MockDaprClient)
		mockClient.On("PublishEvent", mock.Anything, "redis-pubsub", "webhook-events", mock.Anything, mock.Anything).
			Return(context.Canceled)

		pubsubClient := dapr.NewPubSubClient(mockClient, "redis-pubsub", "webhook-events")

		event, _ := domain.NewWebhookEvent("test-123", "github.com", []byte(`{"event": "push"}`))

		ctx, cancel := context.WithCancel(context.Background())
		cancel() // Cancel immediately

		err := pubsubClient.PublishWebhookEvent(ctx, event)
		assert.Error(t, err)

		mockClient.AssertExpectations(t)
	})
}
