package tests

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	"webhook_microservice/internal/domain"
	infraDapr "webhook_microservice/internal/infra/dapr"
)

// Test infrastructure components without complex mocking
// These tests focus on structure and basic functionality

func TestStateClient_Creation(t *testing.T) {
	// Test that StateClient can be created with nil client (for testing)
	stateClient := infraDapr.NewStateClient(nil)
	assert.NotNil(t, stateClient)
}

func TestWebhookDeduplicationService_Creation(t *testing.T) {
	// Test that WebhookDeduplicationService can be created
	stateClient := infraDapr.NewStateClient(nil)
	dedupService := infraDapr.NewWebhookDeduplicationService(stateClient, "test-store")
	assert.NotNil(t, dedupService)
}

func TestPubSubClient_Creation(t *testing.T) {
	// Test that PubSubClient can be created
	logger := zap.NewNop()
	pubsubClient := infraDapr.NewPubSubClient(nil, logger)
	assert.NotNil(t, pubsubClient)
}

func TestWebhookPublishingService_Creation(t *testing.T) {
	// Test that WebhookPublishingService can be created
	logger := zap.NewNop()
	pubsubClient := infraDapr.NewPubSubClient(nil, logger)
	publishingService := infraDapr.NewWebhookPublishingService(pubsubClient, "test-pubsub", "test-topic")
	assert.NotNil(t, publishingService)
}

func TestDomainIntegration_WebhookEventKey(t *testing.T) {
	// Test that webhook events generate correct keys for deduplication
	deliveryID, err := domain.NewDeliveryID("test-123")
	require.NoError(t, err)

	event, err := domain.NewWebhookEvent(deliveryID, "github.com", []byte(`{"event": "push"}`))
	require.NoError(t, err)

	expectedKey := "github.com:test-123"
	assert.Equal(t, expectedKey, event.GetKey())
}

func TestDomainIntegration_DeliveryIDValidation(t *testing.T) {
	// Test delivery ID validation in infrastructure context
	tests := []struct {
		name    string
		id      string
		wantErr bool
	}{
		{"Valid ID", "test-123", false},
		{"Empty ID", "", true},
		{"Whitespace ID", "   ", true},
		{"Very long ID", string(make([]byte, 300)), true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			deliveryID, err := domain.NewDeliveryID(tt.id)
			if tt.wantErr {
				assert.Error(t, err)
				assert.True(t, deliveryID.IsEmpty())
			} else {
				assert.NoError(t, err)
				assert.False(t, deliveryID.IsEmpty())
				assert.Equal(t, tt.id, deliveryID.String())
			}
		})
	}
}

func TestInfrastructure_ServiceCreation(t *testing.T) {
	// Test that all infrastructure services can be created without panics
	logger := zap.NewNop()

	// Test StateClient creation
	stateClient := infraDapr.NewStateClient(nil)
	assert.NotNil(t, stateClient)

	// Test WebhookDeduplicationService creation
	dedupService := infraDapr.NewWebhookDeduplicationService(stateClient, "test-store")
	assert.NotNil(t, dedupService)

	// Test PubSubClient creation
	pubsubClient := infraDapr.NewPubSubClient(nil, logger)
	assert.NotNil(t, pubsubClient)

	// Test WebhookPublishingService creation
	publishingService := infraDapr.NewWebhookPublishingService(pubsubClient, "test-pubsub", "test-topic")
	assert.NotNil(t, publishingService)
}
