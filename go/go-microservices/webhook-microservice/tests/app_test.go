package tests

import (
	"context"
	"errors"
	"log/slog"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"webhook_microservice/internal/app"
	"webhook_microservice/internal/app/commands"
	"webhook_microservice/internal/domain"
)

// Mock implementations for testing
type MockDeduplicationService struct {
	mock.Mock
}

func (m *MockDeduplicationService) IsDuplicate(ctx context.Context, id domain.DeliveryID) (bool, error) {
	args := m.Called(ctx, id)
	return args.Bool(0), args.Error(1)
}

func (m *MockDeduplicationService) MarkProcessed(ctx context.Context, id domain.DeliveryID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

type MockPublishingService struct {
	mock.Mock
}

func (m *MockPublishingService) Publish(ctx context.Context, event domain.WebhookEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func TestWebhookHandler_HandleWebhook_Success_Unit(t *testing.T) {
	// Setup mocks
	mockDedup := new(MockDeduplicationService)
	mockPublish := new(MockPublishingService)
	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))

	// Create handler
	handler := commands.NewWebhookHandler(mockDedup, mockPublish, logger)

	// Create test command
	cmd := commands.HandleWebhookCommand{
		DeliveryID: "test-123",
		Source:     "github.com",
		Payload:    []byte(`{"event": "push"}`),
	}

	// Create expected delivery ID
	deliveryID, err := domain.NewDeliveryID("test-123")
	require.NoError(t, err)

	// Setup expectations
	mockDedup.On("IsDuplicate", mock.Anything, deliveryID).Return(false, nil)
	mockDedup.On("MarkProcessed", mock.Anything, deliveryID).Return(nil)
	mockPublish.On("Publish", mock.Anything, mock.AnythingOfType("domain.WebhookEvent")).Return(nil)

	// Execute
	ctx := context.Background()
	err = handler.HandleWebhook(ctx, cmd)

	// Assert
	assert.NoError(t, err)

	// Verify all expectations were met
	mockDedup.AssertExpectations(t)
	mockPublish.AssertExpectations(t)
}

func TestWebhookHandler_HandleWebhook_Duplicate_Unit(t *testing.T) {
	// Setup mocks
	mockDedup := new(MockDeduplicationService)
	mockPublish := new(MockPublishingService)
	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))

	// Create handler
	handler := commands.NewWebhookHandler(mockDedup, mockPublish, logger)

	// Create test command
	cmd := commands.HandleWebhookCommand{
		DeliveryID: "test-123",
		Source:     "github.com",
		Payload:    []byte(`{"event": "push"}`),
	}

	// Create expected delivery ID
	deliveryID, err := domain.NewDeliveryID("test-123")
	require.NoError(t, err)

	// Setup expectations - webhook is duplicate
	mockDedup.On("IsDuplicate", mock.Anything, deliveryID).Return(true, nil)
	// Should not call MarkProcessed or Publish

	// Execute
	ctx := context.Background()
	err = handler.HandleWebhook(ctx, cmd)

	// Assert - should return duplicate error
	assert.Error(t, err)
	assert.Equal(t, app.ErrDuplicate, err)

	// Verify expectations
	mockDedup.AssertExpectations(t)
	mockPublish.AssertNotCalled(t, "Publish")
}

func TestWebhookHandler_HandleWebhook_DeduplicationCheckError_Unit(t *testing.T) {
	// Setup mocks
	mockDedup := new(MockDeduplicationService)
	mockPublish := new(MockPublishingService)
	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))

	// Create handler
	handler := commands.NewWebhookHandler(mockDedup, mockPublish, logger)

	// Create test command
	cmd := commands.HandleWebhookCommand{
		DeliveryID: "test-123",
		Source:     "github.com",
		Payload:    []byte(`{"event": "push"}`),
	}

	// Create expected delivery ID
	deliveryID, err := domain.NewDeliveryID("test-123")
	require.NoError(t, err)

	// Setup expectations - deduplication check fails
	expectedError := errors.New("deduplication service unavailable")
	mockDedup.On("IsDuplicate", mock.Anything, deliveryID).Return(false, expectedError)

	// Execute
	ctx := context.Background()
	err = handler.HandleWebhook(ctx, cmd)

	// Assert
	assert.Error(t, err)
	assert.Equal(t, app.ErrDeduplicationFailed, err)

	// Verify expectations
	mockDedup.AssertExpectations(t)
	mockPublish.AssertNotCalled(t, "Publish")
}

func TestWebhookHandler_HandleWebhook_InvalidDeliveryID_Unit(t *testing.T) {
	// Setup mocks
	mockDedup := new(MockDeduplicationService)
	mockPublish := new(MockPublishingService)
	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))

	// Create handler
	handler := commands.NewWebhookHandler(mockDedup, mockPublish, logger)

	// Create test command with invalid delivery ID
	cmd := commands.HandleWebhookCommand{
		DeliveryID: "", // Invalid empty delivery ID
		Source:     "github.com",
		Payload:    []byte(`{"event": "push"}`),
	}

	// Execute
	ctx := context.Background()
	err := handler.HandleWebhook(ctx, cmd)

	// Assert
	assert.Error(t, err)
	assert.Equal(t, app.ErrInvalidID, err)

	// Verify no service calls were made
	mockDedup.AssertNotCalled(t, "IsDuplicate")
	mockDedup.AssertNotCalled(t, "MarkProcessed")
	mockPublish.AssertNotCalled(t, "Publish")
}

func TestWebhookHandler_HandleWebhook_InvalidWebhookData_Unit(t *testing.T) {
	// Setup mocks
	mockDedup := new(MockDeduplicationService)
	mockPublish := new(MockPublishingService)
	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))

	// Create handler
	handler := commands.NewWebhookHandler(mockDedup, mockPublish, logger)

	// Create test command with invalid data
	cmd := commands.HandleWebhookCommand{
		DeliveryID: "test-123",
		Source:     "", // Invalid empty source
		Payload:    []byte(`{"event": "push"}`),
	}

	// Execute
	ctx := context.Background()
	err := handler.HandleWebhook(ctx, cmd)

	// Assert
	assert.Error(t, err)
	assert.Equal(t, app.ErrInvalidWebhookData, err)

	// Verify no service calls were made
	mockDedup.AssertNotCalled(t, "IsDuplicate")
	mockDedup.AssertNotCalled(t, "MarkProcessed")
	mockPublish.AssertNotCalled(t, "Publish")
}

func TestWebhookHandler_HandleWebhook_PublishError_Unit(t *testing.T) {
	// Setup mocks
	mockDedup := new(MockDeduplicationService)
	mockPublish := new(MockPublishingService)
	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))

	// Create handler
	handler := commands.NewWebhookHandler(mockDedup, mockPublish, logger)

	// Create test command
	cmd := commands.HandleWebhookCommand{
		DeliveryID: "test-123",
		Source:     "github.com",
		Payload:    []byte(`{"event": "push"}`),
	}

	// Create expected delivery ID
	deliveryID, err := domain.NewDeliveryID("test-123")
	require.NoError(t, err)

	// Setup expectations
	mockDedup.On("IsDuplicate", mock.Anything, deliveryID).Return(false, nil)
	mockDedup.On("MarkProcessed", mock.Anything, deliveryID).Return(nil)
	expectedError := errors.New("failed to publish event")
	mockPublish.On("Publish", mock.Anything, mock.AnythingOfType("domain.WebhookEvent")).Return(expectedError)

	// Execute
	ctx := context.Background()
	err = handler.HandleWebhook(ctx, cmd)

	// Assert
	assert.Error(t, err)
	assert.Equal(t, app.ErrPublishFailed, err)

	// Verify expectations
	mockDedup.AssertExpectations(t)
	mockPublish.AssertExpectations(t)
}

func TestWebhookHandler_HandleWebhook_ContextCancellation_Unit(t *testing.T) {
	// Setup mocks
	mockDedup := new(MockDeduplicationService)
	mockPublish := new(MockPublishingService)
	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))

	// Create handler
	handler := commands.NewWebhookHandler(mockDedup, mockPublish, logger)

	// Create test command
	cmd := commands.HandleWebhookCommand{
		DeliveryID: "test-123",
		Source:     "github.com",
		Payload:    []byte(`{"event": "push"}`),
	}

	// Create expected delivery ID
	deliveryID, err := domain.NewDeliveryID("test-123")
	require.NoError(t, err)

	// Create cancelled context
	ctx, cancel := context.WithCancel(context.Background())
	cancel() // Cancel immediately

	// Setup expectations - should respect context cancellation
	mockDedup.On("IsDuplicate", mock.Anything, deliveryID).Return(false, context.Canceled)

	// Execute
	err = handler.HandleWebhook(ctx, cmd)

	// Assert
	assert.Error(t, err)
	assert.Equal(t, app.ErrDeduplicationFailed, err)

	// Verify expectations
	mockDedup.AssertExpectations(t)
}
