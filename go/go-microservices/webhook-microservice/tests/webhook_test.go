package tests

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"webhook_microservice/internal/api"
	"webhook_microservice/internal/container"
)

// Test configuration constants
const (
	testTimeout = 30 * time.Second
	testPort    = "8080"
	daprPort    = "3500"
)

// setupTestEnvironment prepares the test environment
func setupTestEnvironment() {
	// Set test environment variables
	os.Setenv("WEBHOOK_PORT", testPort)
	os.Setenv("WEBHOOK_DAPR_HTTP_PORT", daprPort)
	os.Setenv("WEBHOOK_STATE_STORE", "statestore")
	os.Setenv("WEBHOOK_PUBSUB_NAME", "redis-pubsub")
	os.Setenv("WEBHOOK_TOPIC_NAME", "webhook-events")
	os.Setenv("GIN_MODE", "test")
}

// createTestContainer creates a container for testing
func createTestContainer() *container.Container {
	setupTestEnvironment()
	return container.NewContainer()
}

// isServiceRunning checks if the webhook service is running
func isServiceRunning() bool {
	client := &http.Client{Timeout: 2 * time.Second}
	resp, err := client.Get(fmt.Sprintf("http://localhost:%s/healthz", testPort))
	if err != nil {
		return false
	}
	defer resp.Body.Close()
	return resp.StatusCode == http.StatusOK
}

func TestWebhookEndpoint_Unit(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create a test container with mocked dependencies
	container := createTestContainer()

	// Create router with test routes (no middleware)
	router := gin.New()
	api.RegisterTestRoutes(router, container)

	tests := []struct {
		name           string
		method         string
		url            string
		headers        map[string]string
		body           interface{}
		expectedStatus int
	}{
		{
			name:   "Valid webhook request (unit test - expects validation failure)",
			method: "POST",
			url:    "/webhook",
			headers: map[string]string{
				"Content-Type":   "application/json",
				"X-Delivery-ID":  "test-123",
				"X-Source":       "github.com",
			},
			body: map[string]interface{}{
				"test": "data",
			},
			expectedStatus: http.StatusBadRequest, // 400 because no middleware validation in unit tests
		},
		{
			name:   "Missing delivery ID",
			method: "POST",
			url:    "/webhook",
			headers: map[string]string{
				"Content-Type": "application/json",
			},
			body: map[string]interface{}{
				"test": "data",
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "Empty payload",
			method:         "POST",
			url:            "/webhook",
			headers: map[string]string{
				"Content-Type":  "application/json",
				"X-Delivery-ID": "test-456",
			},
			body:           "",
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Prepare request body
			var bodyReader *bytes.Reader
			if tt.body != "" {
				bodyBytes, _ := json.Marshal(tt.body)
				bodyReader = bytes.NewReader(bodyBytes)
			} else {
				bodyReader = bytes.NewReader([]byte{})
			}

			// Create request
			req, err := http.NewRequest(tt.method, tt.url, bodyReader)
			assert.NoError(t, err)

			// Set headers
			for key, value := range tt.headers {
				req.Header.Set(key, value)
			}

			// Create response recorder
			w := httptest.NewRecorder()

			// Perform request
			router.ServeHTTP(w, req)

			// Assert status code
			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

func TestHealthEndpoints_Unit(t *testing.T) {
	gin.SetMode(gin.TestMode)

	container := createTestContainer()
	router := gin.New()
	api.RegisterTestRoutes(router, container)

	tests := []struct {
		name           string
		url            string
		expectedStatus int
	}{
		{
			name:           "Health check",
			url:            "/healthz",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "Readiness check",
			url:            "/readyz",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "Metrics endpoint",
			url:            "/metrics",
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := http.NewRequest("GET", tt.url, nil)
			assert.NoError(t, err)

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

// Integration tests that require the service to be running
func TestWebhookEndpoint_Integration(t *testing.T) {
	if !isServiceRunning() {
		t.Skip("Skipping integration tests - webhook service is not running. Start with: bash scripts/run-local.sh")
	}

	baseURL := fmt.Sprintf("http://localhost:%s", testPort)
	client := &http.Client{Timeout: testTimeout}

	tests := []struct {
		name           string
		method         string
		url            string
		headers        map[string]string
		body           interface{}
		expectedStatus int
		description    string
	}{
		{
			name:   "Valid webhook request",
			method: "POST",
			url:    "/webhook",
			headers: map[string]string{
				"Content-Type":   "application/json",
				"X-Delivery-ID":  "integration-test-123",
				"X-Source":       "github.com",
			},
			body: map[string]interface{}{
				"event":      "push",
				"repository": "test-repo",
			},
			expectedStatus: http.StatusOK,
			description:    "Should successfully process a valid webhook",
		},
		{
			name:   "Duplicate webhook request",
			method: "POST",
			url:    "/webhook",
			headers: map[string]string{
				"Content-Type":   "application/json",
				"X-Delivery-ID":  "integration-test-123", // Same ID as above
				"X-Source":       "github.com",
			},
			body: map[string]interface{}{
				"event":      "push",
				"repository": "test-repo",
			},
			expectedStatus: http.StatusOK,
			description:    "Should handle duplicate webhook gracefully",
		},
		{
			name:   "Missing delivery ID",
			method: "POST",
			url:    "/webhook",
			headers: map[string]string{
				"Content-Type": "application/json",
				"X-Source":     "github.com",
			},
			body: map[string]interface{}{
				"test": "data",
			},
			expectedStatus: http.StatusBadRequest,
			description:    "Should reject webhook without delivery ID",
		},
		{
			name:   "Missing source (uses fallback)",
			method: "POST",
			url:    "/webhook",
			headers: map[string]string{
				"Content-Type":  "application/json",
				"X-Delivery-ID": "integration-test-456",
			},
			body: map[string]interface{}{
				"test": "data",
			},
			expectedStatus: http.StatusOK,
			description:    "Should process webhook without source using fallback",
		},
		{
			name:   "Different source webhook",
			method: "POST",
			url:    "/webhook",
			headers: map[string]string{
				"Content-Type":   "application/json",
				"X-Delivery-ID":  "integration-test-gitlab-789",
				"X-Source":       "gitlab.com",
			},
			body: map[string]interface{}{
				"event":   "merge_request",
				"project": "test-project",
			},
			expectedStatus: http.StatusOK,
			description:    "Should process webhook from different source",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Prepare request body
			bodyBytes, err := json.Marshal(tt.body)
			require.NoError(t, err)

			// Create request
			req, err := http.NewRequest(tt.method, baseURL+tt.url, bytes.NewReader(bodyBytes))
			require.NoError(t, err)

			// Set headers
			for key, value := range tt.headers {
				req.Header.Set(key, value)
			}

			// Perform request
			resp, err := client.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			// Assert status code
			assert.Equal(t, tt.expectedStatus, resp.StatusCode, tt.description)

			// Parse response for additional assertions
			var response map[string]interface{}
			err = json.NewDecoder(resp.Body).Decode(&response)
			require.NoError(t, err)

			// Additional assertions based on expected status
			if tt.expectedStatus == http.StatusOK {
				if success, ok := response["success"].(bool); ok {
					assert.True(t, success, "Response should indicate success")
				}
				assert.Contains(t, response, "data", "Response should contain data")
			} else {
				if success, ok := response["success"].(bool); ok {
					assert.False(t, success, "Response should indicate failure")
				}
				assert.Contains(t, response, "error", "Response should contain error")
			}
		})
	}
}

func TestHealthEndpoints_Integration(t *testing.T) {
	if !isServiceRunning() {
		t.Skip("Skipping integration tests - webhook service is not running. Start with: bash scripts/run-local.sh")
	}

	baseURL := fmt.Sprintf("http://localhost:%s", testPort)
	client := &http.Client{Timeout: testTimeout}

	tests := []struct {
		name           string
		url            string
		expectedStatus int
		description    string
	}{
		{
			name:           "Health check",
			url:            "/healthz",
			expectedStatus: http.StatusOK,
			description:    "Health endpoint should return OK",
		},
		{
			name:           "Readiness check",
			url:            "/readyz",
			expectedStatus: http.StatusOK,
			description:    "Readiness endpoint should return OK",
		},
		{
			name:           "Metrics endpoint",
			url:            "/metrics",
			expectedStatus: http.StatusOK,
			description:    "Metrics endpoint should return Prometheus metrics",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := client.Get(baseURL + tt.url)
			require.NoError(t, err)
			defer resp.Body.Close()

			assert.Equal(t, tt.expectedStatus, resp.StatusCode, tt.description)

			// Additional assertions for specific endpoints
			if tt.url == "/healthz" || tt.url == "/readyz" {
				var response map[string]interface{}
				err = json.NewDecoder(resp.Body).Decode(&response)
				require.NoError(t, err)
				if success, ok := response["success"].(bool); ok {
					assert.True(t, success, "Health response should indicate success")
				}
			}
		})
	}
}

func TestDaprIntegration(t *testing.T) {
	if !isServiceRunning() {
		t.Skip("Skipping integration tests - webhook service is not running. Start with: bash scripts/run-local.sh")
	}

	client := &http.Client{Timeout: testTimeout}

	t.Run("Dapr health check", func(t *testing.T) {
		resp, err := client.Get(fmt.Sprintf("http://localhost:%s/v1.0/healthz", daprPort))
		require.NoError(t, err)
		defer resp.Body.Close()

		// Dapr health endpoint returns 204 No Content when healthy
		assert.Equal(t, http.StatusNoContent, resp.StatusCode, "Dapr should be healthy")
	})

	t.Run("Dapr metadata", func(t *testing.T) {
		resp, err := client.Get(fmt.Sprintf("http://localhost:%s/v1.0/metadata", daprPort))
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode, "Dapr metadata should be available")

		var metadata map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&metadata)
		require.NoError(t, err)

		assert.Equal(t, "webhook-microservice", metadata["id"], "Dapr app ID should match")
		assert.Contains(t, metadata, "components", "Metadata should contain components")
	})
}
