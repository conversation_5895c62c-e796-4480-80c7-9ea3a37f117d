package tests

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"webhook_microservice/internal/api"
	"webhook_microservice/internal/container"
)

func TestWebhookEndpoint(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create a test container (you might want to mock dependencies)
	container := container.NewContainer()
	
	// Create router
	router := api.NewRouter(container)

	tests := []struct {
		name           string
		method         string
		url            string
		headers        map[string]string
		body           interface{}
		expectedStatus int
	}{
		{
			name:   "Valid webhook request",
			method: "POST",
			url:    "/webhook",
			headers: map[string]string{
				"Content-Type":   "application/json",
				"X-Delivery-ID":  "test-123",
				"X-Source":       "github.com",
			},
			body: map[string]interface{}{
				"test": "data",
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:   "Missing delivery ID",
			method: "POST",
			url:    "/webhook",
			headers: map[string]string{
				"Content-Type": "application/json",
			},
			body: map[string]interface{}{
				"test": "data",
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "Empty payload",
			method:         "POST",
			url:            "/webhook",
			headers: map[string]string{
				"Content-Type":  "application/json",
				"X-Delivery-ID": "test-456",
			},
			body:           "",
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Prepare request body
			var bodyReader *bytes.Reader
			if tt.body != "" {
				bodyBytes, _ := json.Marshal(tt.body)
				bodyReader = bytes.NewReader(bodyBytes)
			} else {
				bodyReader = bytes.NewReader([]byte{})
			}

			// Create request
			req, err := http.NewRequest(tt.method, tt.url, bodyReader)
			assert.NoError(t, err)

			// Set headers
			for key, value := range tt.headers {
				req.Header.Set(key, value)
			}

			// Create response recorder
			w := httptest.NewRecorder()

			// Perform request
			router.ServeHTTP(w, req)

			// Assert status code
			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

func TestHealthEndpoints(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	container := container.NewContainer()
	router := api.NewRouter(container)

	tests := []struct {
		name           string
		url            string
		expectedStatus int
	}{
		{
			name:           "Health check",
			url:            "/healthz",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "Readiness check",
			url:            "/readyz",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "Metrics endpoint",
			url:            "/metrics",
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := http.NewRequest("GET", tt.url, nil)
			assert.NoError(t, err)

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}
