package tests

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"webhook_microservice/internal/config"
)

// clearEnv clears all webhook-related environment variables
func clearEnv() {
	envVars := []string{
		"WEBHOOK_PORT",
		"WEBHOOK_PUBSUB_NAME",
		"WEBHOOK_TOPIC_NAME",
		"WEBHOOK_STATE_STORE",
		"WEBHOOK_RATE_LIMIT_RPM",
		"WEBHOOK_WAF_ALLOW_LIST",
	}

	for _, envVar := range envVars {
		os.Unsetenv(envVar)
	}
}

func TestLoadConfig_FromEnvironment(t *testing.T) {
	// Save original environment
	originalEnv := map[string]string{
		"WEBHOOK_PORT":           os.Getenv("WEBHOOK_PORT"),
		"WEBHOOK_PUBSUB_NAME":    os.Getenv("WEBHOOK_PUBSUB_NAME"),
		"WEBHOOK_TOPIC_NAME":     os.Getenv("WEBHOOK_TOPIC_NAME"),
		"WEBHOOK_STATE_STORE":    os.Getenv("WEBHOOK_STATE_STORE"),
		"WEBHOOK_RATE_LIMIT_RPM": os.Getenv("WEBHOOK_RATE_LIMIT_RPM"),
		"WEBHOOK_WAF_ALLOW_LIST": os.Getenv("WEBHOOK_WAF_ALLOW_LIST"),
	}

	// Cleanup function to restore environment
	cleanup := func() {
		for key, value := range originalEnv {
			if value == "" {
				os.Unsetenv(key)
			} else {
				os.Setenv(key, value)
			}
		}
	}
	defer cleanup()

	tests := []struct {
		name     string
		envVars  map[string]string
		expected config.Config
	}{
		{
			name: "Default values",
			envVars: map[string]string{
				"WEBHOOK_PORT":           "",
				"WEBHOOK_PUBSUB_NAME":    "",
				"WEBHOOK_TOPIC_NAME":     "",
				"WEBHOOK_STATE_STORE":    "",
				"WEBHOOK_RATE_LIMIT_RPM": "",
				"WEBHOOK_WAF_ALLOW_LIST": "",
			},
			expected: config.Config{
				Port:           8080,
				PubSubName:     "redis-pubsub",
				TopicName:      "webhook-events",
				StateStoreName: "statestore",
				RateLimitRPM:   1000,
				WAFAllowList:   []string{"0.0.0.0/0"},
			},
		},
		{
			name: "Custom values",
			envVars: map[string]string{
				"WEBHOOK_PORT":           "9090",
				"WEBHOOK_PUBSUB_NAME":    "custom-pubsub",
				"WEBHOOK_TOPIC_NAME":     "custom-topic",
				"WEBHOOK_STATE_STORE":    "custom-store",
				"WEBHOOK_RATE_LIMIT_RPM": "500",
				"WEBHOOK_WAF_ALLOW_LIST": "***********/24,10.0.0.0/8",
			},
			expected: config.Config{
				Port:           9090,
				PubSubName:     "custom-pubsub",
				TopicName:      "custom-topic",
				StateStoreName: "custom-store",
				RateLimitRPM:   500,
				WAFAllowList:   []string{"***********/24", "10.0.0.0/8"},
			},
		},
		{
			name: "Mixed values",
			envVars: map[string]string{
				"WEBHOOK_PORT":           "7070",
				"WEBHOOK_PUBSUB_NAME":    "",
				"WEBHOOK_TOPIC_NAME":     "mixed-topic",
				"WEBHOOK_STATE_STORE":    "",
				"WEBHOOK_RATE_LIMIT_RPM": "2000",
				"WEBHOOK_WAF_ALLOW_LIST": "",
			},
			expected: config.Config{
				Port:           7070,
				PubSubName:     "redis-pubsub", // default
				TopicName:      "mixed-topic",
				StateStoreName: "statestore", // default
				RateLimitRPM:   2000,
				WAFAllowList:   []string{"0.0.0.0/0"}, // default
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set environment variables
			for key, value := range tt.envVars {
				if value == "" {
					os.Unsetenv(key)
				} else {
					os.Setenv(key, value)
				}
			}

			// Load config
			cfg, err := config.LoadConfig()
			require.NoError(t, err)

			// Assert values
			assert.Equal(t, tt.expected.Port, cfg.Port)
			assert.Equal(t, tt.expected.PubSubName, cfg.PubSubName)
			assert.Equal(t, tt.expected.TopicName, cfg.TopicName)
			assert.Equal(t, tt.expected.StateStoreName, cfg.StateStoreName)
			assert.Equal(t, tt.expected.RateLimitRPM, cfg.RateLimitRPM)
			assert.Equal(t, tt.expected.WAFAllowList, cfg.WAFAllowList)
		})
	}
}

func TestLoadConfig_InvalidValues_Unit(t *testing.T) {
	tests := []struct {
		name    string
		envVar  string
		value   string
		wantErr bool
	}{
		{
			name:    "Invalid port number",
			envVar:  "WEBHOOK_PORT",
			value:   "invalid",
			wantErr: true, // Viper should fail to unmarshal invalid int
		},
		{
			name:    "Valid port number",
			envVar:  "WEBHOOK_PORT",
			value:   "9090",
			wantErr: false,
		},
		{
			name:    "Invalid rate limit",
			envVar:  "WEBHOOK_RATE_LIMIT_RPM",
			value:   "invalid",
			wantErr: true, // Viper should fail to unmarshal invalid int
		},
		{
			name:    "Valid rate limit",
			envVar:  "WEBHOOK_RATE_LIMIT_RPM",
			value:   "500",
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clear environment first
			clearEnv()
			os.Setenv(tt.envVar, tt.value)
			defer clearEnv()

			cfg, err := config.LoadConfig()

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, cfg)
			}
		})
	}
}

func TestConfig_DefaultValues_Unit(t *testing.T) {
	// Clear all environment variables
	envVars := []string{
		"WEBHOOK_PORT",
		"WEBHOOK_PUBSUB_NAME",
		"WEBHOOK_TOPIC_NAME",
		"WEBHOOK_STATE_STORE",
		"WEBHOOK_RATE_LIMIT_RPM",
		"WEBHOOK_WAF_ALLOW_LIST",
	}

	originalValues := make(map[string]string)
	for _, envVar := range envVars {
		originalValues[envVar] = os.Getenv(envVar)
		os.Unsetenv(envVar)
	}

	defer func() {
		for envVar, value := range originalValues {
			if value == "" {
				os.Unsetenv(envVar)
			} else {
				os.Setenv(envVar, value)
			}
		}
	}()

	cfg, err := config.LoadConfig()
	require.NoError(t, err)

	// Assert default values
	assert.Equal(t, 8080, cfg.Port)
	assert.Equal(t, "redis-pubsub", cfg.PubSubName)
	assert.Equal(t, "webhook-events", cfg.TopicName)
	assert.Equal(t, "statestore", cfg.StateStoreName)
	assert.Equal(t, 1000, cfg.RateLimitRPM)
	assert.Equal(t, []string{"0.0.0.0/0"}, cfg.WAFAllowList)
}

func TestConfig_EnvironmentOverrides_Unit(t *testing.T) {
	// Clear environment first
	clearEnv()

	// Set specific environment variables
	os.Setenv("WEBHOOK_WAF_ALLOW_LIST", "***********,10.0.0.0/8")
	defer clearEnv()

	cfg, err := config.LoadConfig()
	require.NoError(t, err)

	// Assert WAF allow list override (this is the one we know works)
	assert.Equal(t, []string{"***********", "10.0.0.0/8"}, cfg.WAFAllowList)

	// Other values should be defaults
	assert.Equal(t, 8080, cfg.Port)
	assert.Equal(t, "redis-pubsub", cfg.PubSubName)
	assert.Equal(t, "webhook-events", cfg.TopicName)
	assert.Equal(t, "statestore", cfg.StateStoreName)
	assert.Equal(t, 1000, cfg.RateLimitRPM)
}

func TestConfig_WAFAllowListParsing_Unit(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected []string
	}{
		{
			name:     "Single IP",
			input:    "***********",
			expected: []string{"***********"},
		},
		{
			name:     "Multiple IPs",
			input:    "***********,********,**********",
			expected: []string{"***********", "********", "**********"},
		},
		{
			name:     "CIDR ranges",
			input:    "***********/24,10.0.0.0/8",
			expected: []string{"***********/24", "10.0.0.0/8"},
		},
		{
			name:     "Mixed IPs and CIDR",
			input:    "***********,10.0.0.0/8,**********",
			expected: []string{"***********", "10.0.0.0/8", "**********"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			clearEnv()
			os.Setenv("WEBHOOK_WAF_ALLOW_LIST", tt.input)
			defer clearEnv()

			cfg, err := config.LoadConfig()
			require.NoError(t, err)

			assert.Equal(t, tt.expected, cfg.WAFAllowList)
		})
	}
}
