package tests

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"webhook_microservice/internal/api"
)

func TestNewSuccessResponse_Unit(t *testing.T) {
	data := map[string]interface{}{
		"id":   "123",
		"name": "test",
	}

	response := api.NewSuccessResponse(data)

	assert.True(t, response.Success)
	assert.Equal(t, data, response.Data)
	assert.Nil(t, response.Error)
}

func TestNewErrorResponse_Unit(t *testing.T) {
	code := "TEST_ERROR"
	message := "This is a test error"

	response := api.NewErrorResponse(code, message)

	assert.False(t, response.Success)
	assert.Nil(t, response.Data)
	assert.NotNil(t, response.Error)
	assert.Equal(t, code, response.Error.Code)
	assert.Equal(t, message, response.Error.Message)
}

func TestJSON_Unit(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		status         int
		response       *api.Response
		expectedStatus int
		expectedBody   string
	}{
		{
			name:   "Success response",
			status: http.StatusOK,
			response: api.NewSuccessResponse(map[string]string{
				"message": "success",
			}),
			expectedStatus: http.StatusOK,
			expectedBody:   `{"success":true,"data":{"message":"success"}}`,
		},
		{
			name:   "Error response",
			status: http.StatusBadRequest,
			response: api.NewErrorResponse("BAD_REQUEST", "Invalid input"),
			expectedStatus: http.StatusBadRequest,
			expectedBody:   `{"success":false,"error":{"code":"BAD_REQUEST","message":"Invalid input"}}`,
		},
		{
			name:           "Nil data success response",
			status:         http.StatusOK,
			response:       api.NewSuccessResponse(nil),
			expectedStatus: http.StatusOK,
			expectedBody:   `{"success":true}`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			// Call JSON function
			api.JSON(c, tt.status, tt.response)

			// Assert status
			assert.Equal(t, tt.expectedStatus, w.Code)

			// Assert body
			assert.JSONEq(t, tt.expectedBody, w.Body.String())
		})
	}
}

func TestSuccess_Unit(t *testing.T) {
	gin.SetMode(gin.TestMode)

	data := map[string]interface{}{
		"id":     "123",
		"status": "active",
	}

	// Create test context
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	// Call Success function
	api.Success(c, data)

	// Assert status
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse and assert response
	var response api.Response
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.True(t, response.Success)
	assert.Equal(t, data, response.Data)
	assert.Nil(t, response.Error)
}

func TestCreated_Unit(t *testing.T) {
	gin.SetMode(gin.TestMode)

	data := map[string]interface{}{
		"id":   "456",
		"name": "new-resource",
	}

	// Create test context
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	// Call Created function
	api.Created(c, data)

	// Assert status
	assert.Equal(t, http.StatusCreated, w.Code)

	// Parse and assert response
	var response api.Response
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.True(t, response.Success)
	assert.Equal(t, data, response.Data)
	assert.Nil(t, response.Error)
}

func TestBadRequest_Unit(t *testing.T) {
	gin.SetMode(gin.TestMode)

	message := "Invalid request parameters"

	// Create test context
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	// Call BadRequest function
	api.BadRequest(c, message)

	// Assert status
	assert.Equal(t, http.StatusBadRequest, w.Code)

	// Parse and assert response
	var response api.Response
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.False(t, response.Success)
	assert.Nil(t, response.Data)
	assert.NotNil(t, response.Error)
	assert.Equal(t, "BAD_REQUEST", response.Error.Code)
	assert.Equal(t, message, response.Error.Message)
}

func TestNotFound_Unit(t *testing.T) {
	gin.SetMode(gin.TestMode)

	message := "Resource not found"

	// Create test context
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	// Call NotFound function
	api.NotFound(c, message)

	// Assert status
	assert.Equal(t, http.StatusNotFound, w.Code)

	// Parse and assert response
	var response api.Response
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.False(t, response.Success)
	assert.Nil(t, response.Data)
	assert.NotNil(t, response.Error)
	assert.Equal(t, "NOT_FOUND", response.Error.Code)
	assert.Equal(t, message, response.Error.Message)
}

func TestInternalError_Unit(t *testing.T) {
	gin.SetMode(gin.TestMode)

	message := "Internal server error occurred"

	// Create test context
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	// Call InternalError function
	api.InternalError(c, message)

	// Assert status
	assert.Equal(t, http.StatusInternalServerError, w.Code)

	// Parse and assert response
	var response api.Response
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.False(t, response.Success)
	assert.Nil(t, response.Data)
	assert.NotNil(t, response.Error)
	assert.Equal(t, "INTERNAL_ERROR", response.Error.Code)
	assert.Equal(t, message, response.Error.Message)
}

func TestResponseErrorStructure_Unit(t *testing.T) {
	// Test that Error struct can be properly marshaled/unmarshaled
	originalError := &api.Error{
		Code:    "TEST_ERROR",
		Message: "This is a test error message",
	}

	// Marshal to JSON
	jsonData, err := json.Marshal(originalError)
	require.NoError(t, err)

	// Unmarshal back
	var parsedError api.Error
	err = json.Unmarshal(jsonData, &parsedError)
	require.NoError(t, err)

	// Assert structure is preserved
	assert.Equal(t, originalError.Code, parsedError.Code)
	assert.Equal(t, originalError.Message, parsedError.Message)
}

func TestResponseStructure_Unit(t *testing.T) {
	// Test that Response struct can be properly marshaled/unmarshaled
	originalResponse := &api.Response{
		Success: true,
		Data: map[string]interface{}{
			"nested": map[string]interface{}{
				"key": "value",
			},
			"array": []interface{}{1, 2, 3},
		},
		Error: nil,
	}

	// Marshal to JSON
	jsonData, err := json.Marshal(originalResponse)
	require.NoError(t, err)

	// Unmarshal back
	var parsedResponse api.Response
	err = json.Unmarshal(jsonData, &parsedResponse)
	require.NoError(t, err)

	// Assert structure is preserved
	assert.Equal(t, originalResponse.Success, parsedResponse.Success)
	assert.Equal(t, originalResponse.Error, parsedResponse.Error)

	// Check data structure more flexibly (JSON unmarshaling converts numbers to float64)
	assert.NotNil(t, parsedResponse.Data)
	data := parsedResponse.Data.(map[string]interface{})
	assert.Contains(t, data, "nested")
	assert.Contains(t, data, "array")

	nested := data["nested"].(map[string]interface{})
	assert.Equal(t, "value", nested["key"])

	array := data["array"].([]interface{})
	assert.Len(t, array, 3)
	assert.Equal(t, float64(1), array[0]) // JSON numbers become float64
	assert.Equal(t, float64(2), array[1])
	assert.Equal(t, float64(3), array[2])
}
