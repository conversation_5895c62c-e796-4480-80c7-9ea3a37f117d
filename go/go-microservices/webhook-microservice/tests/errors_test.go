package tests

import (
	"errors"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"webhook_microservice/internal/app"
)

func TestErrInvalidID_Unit(t *testing.T) {
	err := app.ErrInvalidID

	assert.Error(t, err)
	assert.Equal(t, "invalid delivery ID", err.Error())
}

func TestErrDuplicate_Unit(t *testing.T) {
	err := app.ErrDuplicate

	assert.Error(t, err)
	assert.Equal(t, "duplicate webhook event", err.Error())
}

func TestErrDeduplicationFailed_Unit(t *testing.T) {
	err := app.ErrDeduplicationFailed

	assert.Error(t, err)
	assert.Equal(t, "deduplication check failed", err.<PERSON>rror())
}

func TestErrPublishFailed_Unit(t *testing.T) {
	err := app.ErrPublishFailed

	assert.Error(t, err)
	assert.Equal(t, "failed to publish webhook event", err.Error())
}

func TestErrInvalidWebhookData_Unit(t *testing.T) {
	err := app.ErrInvalidWebhookData

	assert.Error(t, err)
	assert.Equal(t, "invalid webhook data", err.Error())
}

func TestErrorComparison_Unit(t *testing.T) {
	// Test that errors can be compared using errors.Is
	assert.True(t, errors.Is(app.ErrInvalidID, app.ErrInvalidID))
	assert.True(t, errors.Is(app.ErrDuplicate, app.ErrDuplicate))
	assert.True(t, errors.Is(app.ErrDeduplicationFailed, app.ErrDeduplicationFailed))
	assert.True(t, errors.Is(app.ErrPublishFailed, app.ErrPublishFailed))
	assert.True(t, errors.Is(app.ErrInvalidWebhookData, app.ErrInvalidWebhookData))

	// Test that different errors are not equal
	assert.False(t, errors.Is(app.ErrInvalidID, app.ErrDeduplicationFailed))
	assert.False(t, errors.Is(app.ErrPublishFailed, app.ErrDuplicate))
}

func TestErrorWrapping_Unit(t *testing.T) {
	// Test that errors can be wrapped and unwrapped
	originalErr := errors.New("original error")

	wrappedDedup := errors.Join(app.ErrDeduplicationFailed, originalErr)
	assert.True(t, errors.Is(wrappedDedup, app.ErrDeduplicationFailed))
	assert.True(t, errors.Is(wrappedDedup, originalErr))

	wrappedPublish := errors.Join(app.ErrPublishFailed, originalErr)
	assert.True(t, errors.Is(wrappedPublish, app.ErrPublishFailed))
	assert.True(t, errors.Is(wrappedPublish, originalErr))

	wrappedInvalidID := errors.Join(app.ErrInvalidID, originalErr)
	assert.True(t, errors.Is(wrappedInvalidID, app.ErrInvalidID))
	assert.True(t, errors.Is(wrappedInvalidID, originalErr))
}

func TestErrorMessages_Unit(t *testing.T) {
	// Test that error messages are descriptive and consistent
	errorMessages := map[error]string{
		app.ErrInvalidID:           "invalid delivery ID",
		app.ErrDuplicate:           "duplicate webhook event",
		app.ErrDeduplicationFailed: "deduplication check failed",
		app.ErrPublishFailed:       "failed to publish webhook event",
		app.ErrInvalidWebhookData:  "invalid webhook data",
	}

	for err, expectedMessage := range errorMessages {
		assert.Equal(t, expectedMessage, err.Error())
		assert.NotEmpty(t, err.Error())
	}
}

func TestErrorUniqueness_Unit(t *testing.T) {
	// Test that all errors are unique
	allErrors := []error{
		app.ErrInvalidID,
		app.ErrDuplicate,
		app.ErrDeduplicationFailed,
		app.ErrPublishFailed,
		app.ErrInvalidWebhookData,
	}

	// Check that no two errors are the same
	for i, err1 := range allErrors {
		for j, err2 := range allErrors {
			if i != j {
				assert.False(t, errors.Is(err1, err2),
					"Error %v should not be equal to error %v", err1, err2)
				assert.NotEqual(t, err1.Error(), err2.Error(),
					"Error messages should be unique: %v vs %v", err1.Error(), err2.Error())
			}
		}
	}
}

func TestErrorTypes_Unit(t *testing.T) {
	// Test that errors implement the error interface
	var err error

	err = app.ErrInvalidID
	assert.NotNil(t, err)
	assert.Implements(t, (*error)(nil), err)

	err = app.ErrDuplicate
	assert.NotNil(t, err)
	assert.Implements(t, (*error)(nil), err)

	err = app.ErrDeduplicationFailed
	assert.NotNil(t, err)
	assert.Implements(t, (*error)(nil), err)

	err = app.ErrPublishFailed
	assert.NotNil(t, err)
	assert.Implements(t, (*error)(nil), err)

	err = app.ErrInvalidWebhookData
	assert.NotNil(t, err)
	assert.Implements(t, (*error)(nil), err)
}

func TestErrorInSwitchStatement_Unit(t *testing.T) {
	// Test that errors can be used in switch statements
	testCases := []struct {
		err      error
		expected string
	}{
		{app.ErrInvalidID, "invalid_id"},
		{app.ErrDuplicate, "duplicate"},
		{app.ErrDeduplicationFailed, "deduplication_failed"},
		{app.ErrPublishFailed, "publish_failed"},
		{app.ErrInvalidWebhookData, "invalid_webhook_data"},
	}

	for _, tc := range testCases {
		var result string
		switch {
		case errors.Is(tc.err, app.ErrInvalidID):
			result = "invalid_id"
		case errors.Is(tc.err, app.ErrDuplicate):
			result = "duplicate"
		case errors.Is(tc.err, app.ErrDeduplicationFailed):
			result = "deduplication_failed"
		case errors.Is(tc.err, app.ErrPublishFailed):
			result = "publish_failed"
		case errors.Is(tc.err, app.ErrInvalidWebhookData):
			result = "invalid_webhook_data"
		default:
			result = "unknown"
		}

		assert.Equal(t, tc.expected, result)
	}
}

func TestHTTPStatusCode_Unit(t *testing.T) {
	// Test HTTP status code mapping
	testCases := []struct {
		err            error
		expectedStatus int
	}{
		{app.ErrInvalidID, http.StatusBadRequest},
		{app.ErrInvalidWebhookData, http.StatusBadRequest},
		{app.ErrDuplicate, http.StatusOK},
		{app.ErrPublishFailed, http.StatusBadGateway},
		{app.ErrDeduplicationFailed, http.StatusInternalServerError},
		{errors.New("unknown error"), http.StatusInternalServerError},
	}

	for _, tc := range testCases {
		t.Run(tc.err.Error(), func(t *testing.T) {
			status := app.HTTPStatusCode(tc.err)
			assert.Equal(t, tc.expectedStatus, status)
		})
	}
}

func TestErrorInErrorHandling_Unit(t *testing.T) {
	// Test realistic error handling scenarios
	simulateDeduplicationError := func() error {
		return app.ErrDeduplicationFailed
	}

	simulatePublishError := func() error {
		return app.ErrPublishFailed
	}

	simulateInvalidIDError := func() error {
		return app.ErrInvalidID
	}

	// Test deduplication error handling
	err := simulateDeduplicationError()
	assert.Error(t, err)
	assert.True(t, errors.Is(err, app.ErrDeduplicationFailed))
	assert.Equal(t, http.StatusInternalServerError, app.HTTPStatusCode(err))

	// Test publish error handling
	err = simulatePublishError()
	assert.Error(t, err)
	assert.True(t, errors.Is(err, app.ErrPublishFailed))
	assert.Equal(t, http.StatusBadGateway, app.HTTPStatusCode(err))

	// Test invalid ID error handling
	err = simulateInvalidIDError()
	assert.Error(t, err)
	assert.True(t, errors.Is(err, app.ErrInvalidID))
	assert.Equal(t, http.StatusBadRequest, app.HTTPStatusCode(err))
}
