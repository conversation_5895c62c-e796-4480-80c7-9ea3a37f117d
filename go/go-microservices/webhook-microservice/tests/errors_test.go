package tests

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"webhook_microservice/internal/app"
)

func TestErrWebhookAlreadyProcessed(t *testing.T) {
	err := app.ErrWebhookAlreadyProcessed

	assert.Error(t, err)
	assert.Equal(t, "webhook already processed", err.<PERSON>rror())
}

func TestErrDeduplicationFailed(t *testing.T) {
	err := app.ErrDeduplicationFailed

	assert.Error(t, err)
	assert.Equal(t, "deduplication check failed", err.Error())
}

func TestErrPublishFailed(t *testing.T) {
	err := app.ErrPublishFailed

	assert.Error(t, err)
	assert.Equal(t, "failed to publish webhook event", err.Error())
}

func TestErrProcessingFailed(t *testing.T) {
	err := app.ErrProcessingFailed

	assert.Error(t, err)
	assert.Equal(t, "webhook processing failed", err.Error())
}

func TestErrorComparison(t *testing.T) {
	// Test that errors can be compared using errors.Is
	assert.True(t, errors.Is(app.ErrWebhookAlreadyProcessed, app.ErrWebhookAlreadyProcessed))
	assert.True(t, errors.Is(app.ErrDeduplicationFailed, app.ErrDeduplicationFailed))
	assert.True(t, errors.Is(app.ErrPublishFailed, app.ErrPublishFailed))
	assert.True(t, errors.Is(app.ErrProcessingFailed, app.ErrProcessingFailed))

	// Test that different errors are not equal
	assert.False(t, errors.Is(app.ErrWebhookAlreadyProcessed, app.ErrDeduplicationFailed))
	assert.False(t, errors.Is(app.ErrPublishFailed, app.ErrProcessingFailed))
}

func TestErrorWrapping(t *testing.T) {
	// Test that errors can be wrapped and unwrapped
	originalErr := errors.New("original error")
	
	wrappedDedup := errors.Join(app.ErrDeduplicationFailed, originalErr)
	assert.True(t, errors.Is(wrappedDedup, app.ErrDeduplicationFailed))
	assert.True(t, errors.Is(wrappedDedup, originalErr))

	wrappedPublish := errors.Join(app.ErrPublishFailed, originalErr)
	assert.True(t, errors.Is(wrappedPublish, app.ErrPublishFailed))
	assert.True(t, errors.Is(wrappedPublish, originalErr))

	wrappedProcessing := errors.Join(app.ErrProcessingFailed, originalErr)
	assert.True(t, errors.Is(wrappedProcessing, app.ErrProcessingFailed))
	assert.True(t, errors.Is(wrappedProcessing, originalErr))
}

func TestErrorMessages(t *testing.T) {
	// Test that error messages are descriptive and consistent
	errors := map[error]string{
		app.ErrWebhookAlreadyProcessed: "webhook already processed",
		app.ErrDeduplicationFailed:     "deduplication check failed",
		app.ErrPublishFailed:           "failed to publish webhook event",
		app.ErrProcessingFailed:        "webhook processing failed",
	}

	for err, expectedMessage := range errors {
		assert.Equal(t, expectedMessage, err.Error())
		assert.NotEmpty(t, err.Error())
	}
}

func TestErrorUniqueness(t *testing.T) {
	// Test that all errors are unique
	allErrors := []error{
		app.ErrWebhookAlreadyProcessed,
		app.ErrDeduplicationFailed,
		app.ErrPublishFailed,
		app.ErrProcessingFailed,
	}

	// Check that no two errors are the same
	for i, err1 := range allErrors {
		for j, err2 := range allErrors {
			if i != j {
				assert.False(t, errors.Is(err1, err2), 
					"Error %v should not be equal to error %v", err1, err2)
				assert.NotEqual(t, err1.Error(), err2.Error(),
					"Error messages should be unique: %v vs %v", err1.Error(), err2.Error())
			}
		}
	}
}

func TestErrorTypes(t *testing.T) {
	// Test that errors implement the error interface
	var err error

	err = app.ErrWebhookAlreadyProcessed
	assert.NotNil(t, err)
	assert.Implements(t, (*error)(nil), err)

	err = app.ErrDeduplicationFailed
	assert.NotNil(t, err)
	assert.Implements(t, (*error)(nil), err)

	err = app.ErrPublishFailed
	assert.NotNil(t, err)
	assert.Implements(t, (*error)(nil), err)

	err = app.ErrProcessingFailed
	assert.NotNil(t, err)
	assert.Implements(t, (*error)(nil), err)
}

func TestErrorInSwitchStatement(t *testing.T) {
	// Test that errors can be used in switch statements
	testCases := []struct {
		err      error
		expected string
	}{
		{app.ErrWebhookAlreadyProcessed, "already_processed"},
		{app.ErrDeduplicationFailed, "deduplication_failed"},
		{app.ErrPublishFailed, "publish_failed"},
		{app.ErrProcessingFailed, "processing_failed"},
	}

	for _, tc := range testCases {
		var result string
		switch {
		case errors.Is(tc.err, app.ErrWebhookAlreadyProcessed):
			result = "already_processed"
		case errors.Is(tc.err, app.ErrDeduplicationFailed):
			result = "deduplication_failed"
		case errors.Is(tc.err, app.ErrPublishFailed):
			result = "publish_failed"
		case errors.Is(tc.err, app.ErrProcessingFailed):
			result = "processing_failed"
		default:
			result = "unknown"
		}

		assert.Equal(t, tc.expected, result)
	}
}

func TestErrorInErrorHandling(t *testing.T) {
	// Test realistic error handling scenarios
	simulateDeduplicationError := func() error {
		return app.ErrDeduplicationFailed
	}

	simulatePublishError := func() error {
		return app.ErrPublishFailed
	}

	// Test deduplication error handling
	err := simulateDeduplicationError()
	assert.Error(t, err)
	assert.True(t, errors.Is(err, app.ErrDeduplicationFailed))

	// Test publish error handling
	err = simulatePublishError()
	assert.Error(t, err)
	assert.True(t, errors.Is(err, app.ErrPublishFailed))
}
