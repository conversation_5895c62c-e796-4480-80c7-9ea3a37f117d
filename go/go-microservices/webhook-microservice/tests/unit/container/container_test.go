package container_test

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"template_microservice/internal/container"
)

func TestNew_Success(t *testing.T) {
	// Set environment variables for testing
	os.Setenv("HTTP_PORT", "8080")
	os.Setenv("LOG_LEVEL", "info")
	os.Setenv("DAPR_ENABLED", "false") // Disable Dapr for unit tests
	os.Setenv("CLICKHOUSE_DSN", "")    // Disable ClickHouse for unit tests
	os.Setenv("MONGODB_URI", "")       // Disable MongoDB for unit tests
	defer func() {
		os.Unsetenv("HTTP_PORT")
		os.Unsetenv("LOG_LEVEL")
		os.Unsetenv("DAPR_ENABLED")
		os.Unsetenv("CLICKHOUSE_DSN")
		os.Unsetenv("MONGODB_URI")
	}()

	c, err := container.New()
	require.NoError(t, err)
	require.NotNil(t, c)

	// Test basic configuration
	assert.Equal(t, "8080", c.Config().HTTPPort)
	assert.False(t, c.Config().DaprEnabled)

	// Test logger is initialized
	assert.NotNil(t, c.Logger())

	// Test graceful degradation - services should be nil when dependencies are unavailable
	assert.Nil(t, c.TemplateService())
	assert.Nil(t, c.DaprClient())
	assert.Nil(t, c.EventPublisher())

	// Clean up
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	err = c.Close(ctx)
	assert.NoError(t, err)
}

func TestNew_WithDaprEnabled(t *testing.T) {
	// Set environment variables for Dapr testing
	os.Setenv("DAPR_ENABLED", "true")
	os.Setenv("DAPR_APP_ID", "test-app")
	os.Setenv("DAPR_PUBSUB_NAME", "test-pubsub")
	os.Setenv("CLICKHOUSE_DSN", "")
	os.Setenv("MONGODB_URI", "")
	defer func() {
		os.Unsetenv("DAPR_ENABLED")
		os.Unsetenv("DAPR_APP_ID")
		os.Unsetenv("DAPR_PUBSUB_NAME")
		os.Unsetenv("CLICKHOUSE_DSN")
		os.Unsetenv("MONGODB_URI")
	}()

	c, err := container.New()
	require.NoError(t, err)
	require.NotNil(t, c)

	// Test Dapr configuration
	assert.True(t, c.Config().DaprEnabled)
	assert.Equal(t, "test-app", c.Config().DaprAppID)
	assert.Equal(t, "test-pubsub", c.Config().DaprPubSubName)

	// Note: Dapr clients might be nil if Dapr sidecar is not running
	// This tests graceful degradation

	// Clean up
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	err = c.Close(ctx)
	assert.NoError(t, err)
}

func TestContainer_AccessorMethods(t *testing.T) {
	os.Setenv("DAPR_ENABLED", "false")
	os.Setenv("CLICKHOUSE_DSN", "")
	os.Setenv("MONGODB_URI", "")
	defer func() {
		os.Unsetenv("DAPR_ENABLED")
		os.Unsetenv("CLICKHOUSE_DSN")
		os.Unsetenv("MONGODB_URI")
	}()

	c, err := container.New()
	require.NoError(t, err)
	require.NotNil(t, c)

	// Test all accessor methods
	assert.NotNil(t, c.Logger())
	assert.NotEmpty(t, c.Config().HTTPPort)

	// These should be nil when dependencies are not available
	assert.Nil(t, c.TemplateService())
	assert.Nil(t, c.DaprClient())
	assert.Nil(t, c.StateClient())
	assert.Nil(t, c.PubSubClient())
	assert.Nil(t, c.InvocationClient())
	assert.Nil(t, c.SecretsClient())
	assert.Nil(t, c.EventPublisher())

	// Clean up
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	err = c.Close(ctx)
	assert.NoError(t, err)
}

func TestContainer_ThreadSafety(t *testing.T) {
	// Test that multiple goroutines can safely call New()
	os.Setenv("DAPR_ENABLED", "false")
	os.Setenv("CLICKHOUSE_DSN", "")
	os.Setenv("MONGODB_URI", "")
	defer func() {
		os.Unsetenv("DAPR_ENABLED")
		os.Unsetenv("CLICKHOUSE_DSN")
		os.Unsetenv("MONGODB_URI")
	}()

	const numGoroutines = 10
	containers := make([]*container.Container, numGoroutines)
	errors := make([]error, numGoroutines)

	// Create containers concurrently
	done := make(chan bool, numGoroutines)
	for i := 0; i < numGoroutines; i++ {
		go func(index int) {
			containers[index], errors[index] = container.New()
			done <- true
		}(i)
	}

	// Wait for all goroutines to complete
	for i := 0; i < numGoroutines; i++ {
		<-done
	}

	// Verify all containers were created successfully
	for i := 0; i < numGoroutines; i++ {
		assert.NoError(t, errors[i])
		assert.NotNil(t, containers[i])
	}

	// Clean up all containers
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	for i := 0; i < numGoroutines; i++ {
		if containers[i] != nil {
			err := containers[i].Close(ctx)
			assert.NoError(t, err)
		}
	}
} 