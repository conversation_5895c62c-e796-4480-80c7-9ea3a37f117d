package container_test

import (
	"context"
	"os"
	"testing"
	"time"

	"template_microservice/internal/container"
)

// BenchmarkContainerCreation benchmarks the container creation process
func BenchmarkContainerCreation(b *testing.B) {
	// Set up environment for benchmarking (disable external dependencies)
	os.Setenv("DAPR_ENABLED", "false")
	os.Setenv("CLICKHOUSE_DSN", "")
	os.Setenv("MONGODB_URI", "")
	os.Setenv("TRACING_ENABLED", "false")
	defer func() {
		os.Unsetenv("DAPR_ENABLED")
		os.Unsetenv("CLICKHOUSE_DSN")
		os.Unsetenv("MONGODB_URI")
		os.Unsetenv("TRACING_ENABLED")
	}()

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		c, err := container.New()
		if err != nil {
			b.<PERSON>alf("Failed to create container: %v", err)
		}

		// Clean up
		ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
		c.Close(ctx)
		cancel()
	}
}

// BenchmarkContainerAccess benchmarks accessing container methods
func BenchmarkContainerAccess(b *testing.B) {
	// Set up environment
	os.Setenv("DAPR_ENABLED", "false")
	os.Setenv("CLICKHOUSE_DSN", "")
	os.Setenv("MONGODB_URI", "")
	defer func() {
		os.Unsetenv("DAPR_ENABLED")
		os.Unsetenv("CLICKHOUSE_DSN")
		os.Unsetenv("MONGODB_URI")
	}()

	c, err := container.New()
	if err != nil {
		b.Fatalf("Failed to create container: %v", err)
	}
	defer func() {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		c.Close(ctx)
	}()

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		// Benchmark accessing various methods
		_ = c.Logger()
		_ = c.Config()
		_ = c.TemplateService()
		_ = c.DaprClient()
		_ = c.EventPublisher()
	}
}

// BenchmarkHealthCheck benchmarks the health check operation
func BenchmarkHealthCheck(b *testing.B) {
	// Set up environment
	os.Setenv("DAPR_ENABLED", "false")
	os.Setenv("CLICKHOUSE_DSN", "")
	os.Setenv("MONGODB_URI", "")
	defer func() {
		os.Unsetenv("DAPR_ENABLED")
		os.Unsetenv("CLICKHOUSE_DSN")
		os.Unsetenv("MONGODB_URI")
	}()

	c, err := container.New()
	if err != nil {
		b.Fatalf("Failed to create container: %v", err)
	}
	defer func() {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		c.Close(ctx)
	}()

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
		_ = c.HealthCheck(ctx)
		cancel()
	}
}

// BenchmarkConcurrentContainerAccess benchmarks concurrent access to container methods
func BenchmarkConcurrentContainerAccess(b *testing.B) {
	// Set up environment
	os.Setenv("DAPR_ENABLED", "false")
	os.Setenv("CLICKHOUSE_DSN", "")
	os.Setenv("MONGODB_URI", "")
	defer func() {
		os.Unsetenv("DAPR_ENABLED")
		os.Unsetenv("CLICKHOUSE_DSN")
		os.Unsetenv("MONGODB_URI")
	}()

	c, err := container.New()
	if err != nil {
		b.Fatalf("Failed to create container: %v", err)
	}
	defer func() {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		c.Close(ctx)
	}()

	b.ResetTimer()
	b.ReportAllocs()

	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			// Access methods concurrently
			_ = c.Logger()
			_ = c.Config()
			_ = c.TemplateService()
		}
	})
} 