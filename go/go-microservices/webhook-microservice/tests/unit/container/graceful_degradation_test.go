package container_test

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"template_microservice/internal/container"
)

// TestGracefulDegradation_DatabaseUnavailable tests that the container
// continues to work when databases are unavailable
func TestGracefulDegradation_DatabaseUnavailable(t *testing.T) {
	// Set invalid database connections
	os.Setenv("CLICKHOUSE_DSN", "tcp://invalid-host:9000/test")
	os.Setenv("MONGODB_URI", "mongodb://invalid-host:27017")
	os.Setenv("DAPR_ENABLED", "false") // Disable Dapr for this test
	defer func() {
		os.Unsetenv("CLICKHOUSE_DSN")
		os.Unsetenv("MONGODB_URI")
		os.Unsetenv("DAPR_ENABLED")
	}()

	// Container should still initialize successfully
	c, err := container.New()
	require.NoError(t, err)
	require.NotNil(t, c)

	// Basic functionality should work
	assert.NotNil(t, c.Logger())
	assert.NotEmpty(t, c.Config().HTTPPort)

	// Database-dependent services should be nil (graceful degradation)
	assert.Nil(t, c.TemplateService())

	// Health check should still work (no critical failures)
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	err = c.HealthCheck(ctx)
	// Health check might fail due to database issues, but container should still be functional
	// The important thing is that the container was created successfully

	// Clean up
	err = c.Close(ctx)
	assert.NoError(t, err)
}

// TestGracefulDegradation_DaprUnavailable tests that the container
// works when Dapr is enabled but unavailable
func TestGracefulDegradation_DaprUnavailable(t *testing.T) {
	// Enable Dapr but it won't be available in test environment
	os.Setenv("DAPR_ENABLED", "true")
	os.Setenv("DAPR_APP_ID", "test-app")
	os.Setenv("DAPR_PUBSUB_NAME", "test-pubsub")
	os.Setenv("CLICKHOUSE_DSN", "")  // Disable databases
	os.Setenv("MONGODB_URI", "")
	defer func() {
		os.Unsetenv("DAPR_ENABLED")
		os.Unsetenv("DAPR_APP_ID")
		os.Unsetenv("DAPR_PUBSUB_NAME")
		os.Unsetenv("CLICKHOUSE_DSN")
		os.Unsetenv("MONGODB_URI")
	}()

	// Container should still initialize successfully despite Dapr being unavailable
	c, err := container.New()
	require.NoError(t, err)
	require.NotNil(t, c)

	// Basic functionality should work
	assert.NotNil(t, c.Logger())
	assert.True(t, c.Config().DaprEnabled)

	// Dapr-dependent services should be nil (graceful degradation)
	// Note: In a real environment without Dapr sidecar, these would be nil
	// In tests, they might still be nil due to connection failures

	// Clean up
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	err = c.Close(ctx)
	assert.NoError(t, err)
}

// TestPartialInitialization tests scenarios where some components
// initialize successfully while others fail
func TestPartialInitialization(t *testing.T) {
	// Set up a scenario where some components might work and others don't
	os.Setenv("DAPR_ENABLED", "false")        // Disable Dapr
	os.Setenv("CLICKHOUSE_DSN", "")           // Disable ClickHouse
	os.Setenv("MONGODB_URI", "")              // Disable MongoDB
	os.Setenv("TRACING_ENABLED", "true")      // Enable tracing
	os.Setenv("JAEGER_ENDPOINT", "http://localhost:14268/api/traces") // But Jaeger might not be available
	defer func() {
		os.Unsetenv("DAPR_ENABLED")
		os.Unsetenv("CLICKHOUSE_DSN")
		os.Unsetenv("MONGODB_URI")
		os.Unsetenv("TRACING_ENABLED")
		os.Unsetenv("JAEGER_ENDPOINT")
	}()

	c, err := container.New()
	require.NoError(t, err)
	require.NotNil(t, c)

	// Core components should always work
	assert.NotNil(t, c.Logger())
	assert.NotEmpty(t, c.Config().HTTPPort)

	// Optional components should gracefully degrade
	assert.Nil(t, c.TemplateService()) // No repositories available
	assert.Nil(t, c.DaprClient())      // Dapr disabled
	assert.Nil(t, c.EventPublisher())  // No Dapr client

	// Clean up
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	err = c.Close(ctx)
	assert.NoError(t, err)
}

// TestEnvironmentConfiguration tests different environment configurations
func TestEnvironmentConfiguration(t *testing.T) {
	testCases := []struct {
		name     string
		envVars  map[string]string
		validate func(t *testing.T, c *container.Container)
	}{
		{
			name: "Production-like configuration",
			envVars: map[string]string{
				"ENVIRONMENT":        "production",
				"LOG_LEVEL":          "warn",
				"PROMETHEUS_ENABLED": "true",
				"TRACING_ENABLED":    "true",
				"DAPR_ENABLED":       "true",
				"CLICKHOUSE_DSN":     "",
				"MONGODB_URI":        "",
			},
			validate: func(t *testing.T, c *container.Container) {
				config := c.Config()
				assert.Equal(t, "production", config.Environment)
				assert.True(t, config.PrometheusEnabled)
				assert.True(t, config.TracingEnabled)
				assert.True(t, config.DaprEnabled)
			},
		},
		{
			name: "Development configuration",
			envVars: map[string]string{
				"ENVIRONMENT":        "development",
				"LOG_LEVEL":          "debug",
				"PROMETHEUS_ENABLED": "false",
				"TRACING_ENABLED":    "false",
				"DAPR_ENABLED":       "false",
				"CLICKHOUSE_DSN":     "",
				"MONGODB_URI":        "",
			},
			validate: func(t *testing.T, c *container.Container) {
				config := c.Config()
				assert.Equal(t, "development", config.Environment)
				assert.False(t, config.PrometheusEnabled)
				assert.False(t, config.TracingEnabled)
				assert.False(t, config.DaprEnabled)
			},
		},
		{
			name: "Minimal configuration",
			envVars: map[string]string{
				"CLICKHOUSE_DSN": "",
				"MONGODB_URI":    "",
				"DAPR_ENABLED":   "false",
			},
			validate: func(t *testing.T, c *container.Container) {
				config := c.Config()
				// Should use defaults
				assert.Equal(t, "8080", config.HTTPPort)
				assert.Equal(t, "development", config.Environment)
				assert.False(t, config.DaprEnabled)
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Set environment variables
			for key, value := range tc.envVars {
				os.Setenv(key, value)
			}
			defer func() {
				for key := range tc.envVars {
					os.Unsetenv(key)
				}
			}()

			c, err := container.New()
			require.NoError(t, err)
			require.NotNil(t, c)

			tc.validate(t, c)

			// Clean up
			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()
			err = c.Close(ctx)
			assert.NoError(t, err)
		})
	}
}

// TestConcurrentAccess tests that the container is safe for concurrent access
func TestConcurrentAccess(t *testing.T) {
	os.Setenv("DAPR_ENABLED", "false")
	os.Setenv("CLICKHOUSE_DSN", "")
	os.Setenv("MONGODB_URI", "")
	defer func() {
		os.Unsetenv("DAPR_ENABLED")
		os.Unsetenv("CLICKHOUSE_DSN")
		os.Unsetenv("MONGODB_URI")
	}()

	c, err := container.New()
	require.NoError(t, err)
	require.NotNil(t, c)

	// Test concurrent access to accessor methods
	const numGoroutines = 50
	done := make(chan bool, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func() {
			defer func() { done <- true }()

			// Access various methods concurrently
			logger := c.Logger()
			assert.NotNil(t, logger)

			config := c.Config()
			assert.NotEmpty(t, config.HTTPPort)

			// These might be nil, but should not panic
			_ = c.TemplateService()
			_ = c.DaprClient()
			_ = c.EventPublisher()

			// Health check should be safe to call concurrently
			ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
			defer cancel()
			_ = c.HealthCheck(ctx)
		}()
	}

	// Wait for all goroutines to complete
	for i := 0; i < numGoroutines; i++ {
		select {
		case <-done:
			// Success
		case <-time.After(10 * time.Second):
			t.Fatal("Timeout waiting for concurrent access test")
		}
	}

	// Clean up
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	err = c.Close(ctx)
	assert.NoError(t, err)
} 