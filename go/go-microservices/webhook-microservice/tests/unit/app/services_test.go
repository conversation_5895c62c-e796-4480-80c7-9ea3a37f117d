package app_test

import (
	"context"
	"errors"
	"log/slog"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"template_microservice/internal/app"
	"template_microservice/internal/domain"
)

// MockTemplateRepository is a mock implementation of TemplateRepository
type MockTemplateRepository struct {
	mock.Mock
}

func (m *MockTemplateRepository) Create(template *domain.Template) error {
	args := m.Called(template)
	return args.Error(0)
}

func (m *MockTemplateRepository) GetByID(id string) (*domain.Template, error) {
	args := m.Called(id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.Template), args.Error(1)
}

// MockEventPublisher is a mock implementation of EventPublisher
type MockEventPublisher struct {
	mock.Mock
}

func (m *MockEventPublisher) PublishTemplateCreated(ctx context.Context, template *domain.Template) error {
	args := m.Called(ctx, template)
	return args.Error(0)
}

func (m *MockEventPublisher) PublishTemplateUpdated(ctx context.Context, template *domain.Template) error {
	args := m.Called(ctx, template)
	return args.Error(0)
}

func (m *MockEventPublisher) PublishTemplateDeleted(ctx context.Context, templateID string) error {
	args := m.Called(ctx, templateID)
	return args.Error(0)
}

func TestTemplateService_CreateTemplate_Success(t *testing.T) {
	// Setup
	mockRepo := new(MockTemplateRepository)
	mockEventPublisher := new(MockEventPublisher)
	logger := slog.New(slog.NewJSONHandler(os.Stdout, nil))

	service := app.NewTemplateService(mockRepo, nil, mockEventPublisher, logger)

	// Setup expectations
	mockRepo.On("Create", mock.AnythingOfType("*domain.Template")).Return(nil)
	mockEventPublisher.On("PublishTemplateCreated", mock.Anything, mock.AnythingOfType("*domain.Template")).Return(nil)

	// Execute
	ctx := context.Background()
	cmd := app.CreateTemplateCommand{
		Name:    "test-template",
		Content: "test content",
	}

	result, err := service.CreateTemplate(ctx, cmd)

	// Assert
	require.NoError(t, err)
	require.NotNil(t, result)
	assert.Equal(t, "test-template", result.Name)
	assert.Equal(t, "test content", result.Content)
	assert.NotEmpty(t, result.ID)

	// Verify mocks
	mockRepo.AssertExpectations(t)
	mockEventPublisher.AssertExpectations(t)
}

func TestTemplateService_CreateTemplate_RepositoryError(t *testing.T) {
	// Setup
	mockRepo := new(MockTemplateRepository)
	mockEventPublisher := new(MockEventPublisher)
	logger := slog.New(slog.NewJSONHandler(os.Stdout, nil))

	service := app.NewTemplateService(mockRepo, nil, mockEventPublisher, logger)

	// Setup expectations
	expectedError := errors.New("repository error")
	mockRepo.On("Create", mock.AnythingOfType("*domain.Template")).Return(expectedError)

	// Execute
	ctx := context.Background()
	cmd := app.CreateTemplateCommand{
		Name:    "test-template",
		Content: "test content",
	}

	result, err := service.CreateTemplate(ctx, cmd)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Equal(t, expectedError, err)

	// Verify mocks
	mockRepo.AssertExpectations(t)
	// Event publisher should not be called if repository fails
	mockEventPublisher.AssertNotCalled(t, "PublishTemplateCreated")
}

func TestTemplateService_CreateTemplate_EventPublisherError(t *testing.T) {
	// Setup
	mockRepo := new(MockTemplateRepository)
	mockEventPublisher := new(MockEventPublisher)
	logger := slog.New(slog.NewJSONHandler(os.Stdout, nil))

	service := app.NewTemplateService(mockRepo, nil, mockEventPublisher, logger)

	// Setup expectations
	mockRepo.On("Create", mock.AnythingOfType("*domain.Template")).Return(nil)
	eventError := errors.New("event publisher error")
	mockEventPublisher.On("PublishTemplateCreated", mock.Anything, mock.AnythingOfType("*domain.Template")).Return(eventError)

	// Execute
	ctx := context.Background()
	cmd := app.CreateTemplateCommand{
		Name:    "test-template",
		Content: "test content",
	}

	result, err := service.CreateTemplate(ctx, cmd)

	// Assert - should still succeed even if event publishing fails
	require.NoError(t, err)
	require.NotNil(t, result)
	assert.Equal(t, "test-template", result.Name)

	// Verify mocks
	mockRepo.AssertExpectations(t)
	mockEventPublisher.AssertExpectations(t)
}

func TestTemplateService_CreateTemplate_NoEventPublisher(t *testing.T) {
	// Setup
	mockRepo := new(MockTemplateRepository)
	logger := slog.New(slog.NewJSONHandler(os.Stdout, nil))

	// Create service without event publisher
	service := app.NewTemplateService(mockRepo, nil, nil, logger)

	// Setup expectations
	mockRepo.On("Create", mock.AnythingOfType("*domain.Template")).Return(nil)

	// Execute
	ctx := context.Background()
	cmd := app.CreateTemplateCommand{
		Name:    "test-template",
		Content: "test content",
	}

	result, err := service.CreateTemplate(ctx, cmd)

	// Assert
	require.NoError(t, err)
	require.NotNil(t, result)
	assert.Equal(t, "test-template", result.Name)

	// Verify mocks
	mockRepo.AssertExpectations(t)
}

func TestTemplateService_GetTemplate_Success(t *testing.T) {
	// Setup
	mockRepo := new(MockTemplateRepository)
	logger := slog.New(slog.NewJSONHandler(os.Stdout, nil))

	service := app.NewTemplateService(nil, mockRepo, nil, logger)

	// Create test template
	template := domain.NewTemplate("test-template", "test content")
	
	// Setup expectations
	mockRepo.On("GetByID", "test-id").Return(template, nil)

	// Execute
	query := app.GetTemplateQuery{ID: "test-id"}
	result, err := service.GetTemplate(query)

	// Assert
	require.NoError(t, err)
	require.NotNil(t, result)
	assert.Equal(t, template.ID, result.ID)
	assert.Equal(t, template.Name, result.Name)

	// Verify mocks
	mockRepo.AssertExpectations(t)
}

func TestTemplateService_GetTemplate_NotFound(t *testing.T) {
	// Setup
	mockRepo := new(MockTemplateRepository)
	logger := slog.New(slog.NewJSONHandler(os.Stdout, nil))

	service := app.NewTemplateService(nil, mockRepo, nil, logger)

	// Setup expectations
	mockRepo.On("GetByID", "non-existent-id").Return(nil, app.ErrTemplateNotFound)

	// Execute
	query := app.GetTemplateQuery{ID: "non-existent-id"}
	result, err := service.GetTemplate(query)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Equal(t, app.ErrTemplateNotFound, err)

	// Verify mocks
	mockRepo.AssertExpectations(t)
}

func TestTemplateService_GetTemplate_RepositoryError(t *testing.T) {
	// Setup
	mockRepo := new(MockTemplateRepository)
	logger := slog.New(slog.NewJSONHandler(os.Stdout, nil))

	service := app.NewTemplateService(nil, mockRepo, nil, logger)

	// Setup expectations
	expectedError := errors.New("repository error")
	mockRepo.On("GetByID", "test-id").Return(nil, expectedError)

	// Execute
	query := app.GetTemplateQuery{ID: "test-id"}
	result, err := service.GetTemplate(query)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Equal(t, expectedError, err)

	// Verify mocks
	mockRepo.AssertExpectations(t)
} 