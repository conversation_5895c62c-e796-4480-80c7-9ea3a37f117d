package messaging_test

import (
	"context"
	"errors"
	"log/slog"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"template_microservice/internal/domain"
	"template_microservice/internal/infra/messaging"
)

// MockDaprClient is a mock implementation of the Dapr client
type MockDaprClient struct {
	mock.Mock
}

func (m *MockDaprClient) PublishEvent(ctx context.Context, pubsubName, topic string, data interface{}) error {
	args := m.Called(ctx, pubsubName, topic, data)
	return args.Error(0)
}

func (m *MockDaprClient) Close() {
	m.Called()
}

func TestNewDaprEventPublisher_Success(t *testing.T) {
	mockClient := new(MockDaprClient)
	logger := slog.New(slog.NewJSONHandler(os.Stdout, nil))

	publisher, err := messaging.NewDaprEventPublisher(mockClient, "test-pubsub", "test-app", logger)

	require.NoError(t, err)
	require.NotNil(t, publisher)
}

func TestNewDaprEventPublisher_NilClient(t *testing.T) {
	logger := slog.New(slog.NewJSONHandler(os.Stdout, nil))

	publisher, err := messaging.NewDaprEventPublisher(nil, "test-pubsub", "test-app", logger)

	assert.Error(t, err)
	assert.Nil(t, publisher)
	assert.Contains(t, err.Error(), "dapr client cannot be nil")
}

func TestNewDaprEventPublisher_EmptyPubsubName(t *testing.T) {
	mockClient := new(MockDaprClient)
	logger := slog.New(slog.NewJSONHandler(os.Stdout, nil))

	publisher, err := messaging.NewDaprEventPublisher(mockClient, "", "test-app", logger)

	assert.Error(t, err)
	assert.Nil(t, publisher)
	assert.Contains(t, err.Error(), "pubsub name cannot be empty")
}

func TestNewDaprEventPublisher_EmptyAppID(t *testing.T) {
	mockClient := new(MockDaprClient)
	logger := slog.New(slog.NewJSONHandler(os.Stdout, nil))

	publisher, err := messaging.NewDaprEventPublisher(mockClient, "test-pubsub", "", logger)

	assert.Error(t, err)
	assert.Nil(t, publisher)
	assert.Contains(t, err.Error(), "app ID cannot be empty")
}

func TestPublishTemplateCreated_Success(t *testing.T) {
	mockClient := new(MockDaprClient)
	logger := slog.New(slog.NewJSONHandler(os.Stdout, nil))

	publisher, err := messaging.NewDaprEventPublisher(mockClient, "test-pubsub", "test-app", logger)
	require.NoError(t, err)

	template := domain.NewTemplate("test-template", "test content")

	// Setup expectations
	mockClient.On("PublishEvent", mock.Anything, "test-pubsub", "template-events", mock.MatchedBy(func(data interface{}) bool {
		event, ok := data.(map[string]interface{})
		if !ok {
			return false
		}
		return event["eventType"] == "template.created" &&
			event["templateId"] == template.ID &&
			event["templateName"] == template.Name &&
			event["source"] == "test-app"
	})).Return(nil)

	// Execute
	ctx := context.Background()
	err = publisher.PublishTemplateCreated(ctx, template)

	// Assert
	assert.NoError(t, err)
	mockClient.AssertExpectations(t)
}

func TestPublishTemplateCreated_DaprError(t *testing.T) {
	mockClient := new(MockDaprClient)
	logger := slog.New(slog.NewJSONHandler(os.Stdout, nil))

	publisher, err := messaging.NewDaprEventPublisher(mockClient, "test-pubsub", "test-app", logger)
	require.NoError(t, err)

	template := domain.NewTemplate("test-template", "test content")

	// Setup expectations
	expectedError := errors.New("dapr publish error")
	mockClient.On("PublishEvent", mock.Anything, "test-pubsub", "template-events", mock.Anything).Return(expectedError)

	// Execute
	ctx := context.Background()
	err = publisher.PublishTemplateCreated(ctx, template)

	// Assert
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to publish event to topic template-events")
	mockClient.AssertExpectations(t)
}

func TestPublishTemplateUpdated_Success(t *testing.T) {
	mockClient := new(MockDaprClient)
	logger := slog.New(slog.NewJSONHandler(os.Stdout, nil))

	publisher, err := messaging.NewDaprEventPublisher(mockClient, "test-pubsub", "test-app", logger)
	require.NoError(t, err)

	template := domain.NewTemplate("test-template", "updated content")

	// Setup expectations
	mockClient.On("PublishEvent", mock.Anything, "test-pubsub", "template-events", mock.MatchedBy(func(data interface{}) bool {
		event, ok := data.(map[string]interface{})
		if !ok {
			return false
		}
		return event["eventType"] == "template.updated" &&
			event["templateId"] == template.ID &&
			event["templateName"] == template.Name &&
			event["source"] == "test-app"
	})).Return(nil)

	// Execute
	ctx := context.Background()
	err = publisher.PublishTemplateUpdated(ctx, template)

	// Assert
	assert.NoError(t, err)
	mockClient.AssertExpectations(t)
}

func TestPublishTemplateDeleted_Success(t *testing.T) {
	mockClient := new(MockDaprClient)
	logger := slog.New(slog.NewJSONHandler(os.Stdout, nil))

	publisher, err := messaging.NewDaprEventPublisher(mockClient, "test-pubsub", "test-app", logger)
	require.NoError(t, err)

	templateID := "test-template-id"

	// Setup expectations
	mockClient.On("PublishEvent", mock.Anything, "test-pubsub", "template-events", mock.MatchedBy(func(data interface{}) bool {
		event, ok := data.(map[string]interface{})
		if !ok {
			return false
		}
		return event["eventType"] == "template.deleted" &&
			event["templateId"] == templateID &&
			event["source"] == "test-app"
	})).Return(nil)

	// Execute
	ctx := context.Background()
	err = publisher.PublishTemplateDeleted(ctx, templateID)

	// Assert
	assert.NoError(t, err)
	mockClient.AssertExpectations(t)
}

func TestPublishTemplateDeleted_DaprError(t *testing.T) {
	mockClient := new(MockDaprClient)
	logger := slog.New(slog.NewJSONHandler(os.Stdout, nil))

	publisher, err := messaging.NewDaprEventPublisher(mockClient, "test-pubsub", "test-app", logger)
	require.NoError(t, err)

	templateID := "test-template-id"

	// Setup expectations
	expectedError := errors.New("dapr publish error")
	mockClient.On("PublishEvent", mock.Anything, "test-pubsub", "template-events", mock.Anything).Return(expectedError)

	// Execute
	ctx := context.Background()
	err = publisher.PublishTemplateDeleted(ctx, templateID)

	// Assert
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to publish event to topic template-events")
	mockClient.AssertExpectations(t)
} 