package e2e

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"template_microservice/internal/domain"
)

const (
	baseURL = "http://localhost:8080"
)

func TestMain(m *testing.M) {
	// Wait for all services to be ready
	waitForServices()

	// Run tests
	code := m.Run()

	// Cleanup
	os.Exit(code)
}

func waitForServices() {
	// Wait for service to be ready
	for i := 0; i < 30; i++ {
		resp, err := http.Get(baseURL + "/readyz")
		if err == nil && resp.StatusCode == http.StatusOK {
			return
		}
		time.Sleep(time.Second)
	}
	panic("services not ready after 30 seconds")
}

func TestTemplateLifecycle(t *testing.T) {
	// Test data
	template := domain.Template{
		Name:    "E2E Test Template",
		Content: "E2E Test Content",
	}

	// Create template
	createResp, err := createTemplate(template)
	require.NoError(t, err)
	assert.NotEmpty(t, createResp.ID)
	assert.Equal(t, template.Name, createResp.Name)
	assert.Equal(t, template.Content, createResp.Content)

	// Get template
	getResp, err := getTemplate(createResp.ID)
	require.NoError(t, err)
	assert.Equal(t, createResp.ID, getResp.ID)
	assert.Equal(t, template.Name, getResp.Name)
	assert.Equal(t, template.Content, getResp.Content)

	// Verify template in ClickHouse
	clickhouseResp, err := getTemplateFromClickHouse(createResp.ID)
	require.NoError(t, err)
	assert.Equal(t, createResp.ID, clickhouseResp.ID)
	assert.Equal(t, template.Name, clickhouseResp.Name)
	assert.Equal(t, template.Content, clickhouseResp.Content)

	// Verify template in MongoDB
	mongoResp, err := getTemplateFromMongoDB(createResp.ID)
	require.NoError(t, err)
	assert.Equal(t, createResp.ID, mongoResp.ID)
	assert.Equal(t, template.Name, mongoResp.Name)
	assert.Equal(t, template.Content, mongoResp.Content)

	// Verify template in Redis cache
	redisResp, err := getTemplateFromRedis(createResp.ID)
	require.NoError(t, err)
	assert.Equal(t, createResp.ID, redisResp.ID)
	assert.Equal(t, template.Name, redisResp.Name)
	assert.Equal(t, template.Content, redisResp.Content)
}

func createTemplate(template domain.Template) (*domain.Template, error) {
	body, err := json.Marshal(template)
	if err != nil {
		return nil, err
	}

	resp, err := http.Post(baseURL+"/api/v1/templates", "application/json", bytes.NewBuffer(body))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	var result domain.Template
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, err
	}

	return &result, nil
}

func getTemplate(id string) (*domain.Template, error) {
	resp, err := http.Get(baseURL + "/api/v1/templates/" + id)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	var result domain.Template
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, err
	}

	return &result, nil
}

func getTemplateFromClickHouse(id string) (*domain.Template, error) {
	// TODO: Implement ClickHouse verification
	return nil, fmt.Errorf("not implemented")
}

func getTemplateFromMongoDB(id string) (*domain.Template, error) {
	// TODO: Implement MongoDB verification
	return nil, fmt.Errorf("not implemented")
}

func getTemplateFromRedis(id string) (*domain.Template, error) {
	// TODO: Implement Redis verification
	return nil, fmt.Errorf("not implemented")
} 