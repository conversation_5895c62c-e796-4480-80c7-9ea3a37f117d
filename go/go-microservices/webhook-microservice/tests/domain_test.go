package tests

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"webhook_microservice/internal/domain"
)

// Domain Layer Tests - Unit Tests
// These tests verify the core business logic and domain models
// without any external dependencies.

// ============================================================================
// DeliveryID Tests - Unit Tests
// ============================================================================

func TestDeliveryID_NewDeliveryID_Unit(t *testing.T) {
	tests := []struct {
		name        string
		raw         string
		expectError bool
		expectedID  string
	}{
		{
			name:        "Valid delivery ID",
			raw:         "test-123",
			expectError: false,
			expectedID:  "test-123",
		},
		{
			name:        "Empty delivery ID",
			raw:         "",
			expectError: true,
		},
		{
			name:        "Whitespace delivery ID",
			raw:         "   ",
			expectError: true,
		},
		{
			name:        "Very long delivery ID",
			raw:         string(make([]byte, 256)),
			expectError: true,
		},
		{
			name:        "Max length delivery ID",
			raw:         string(make([]byte, 255)),
			expectError: false,
			expectedID:  string(make([]byte, 255)),
		},
		{
			name:        "Delivery ID with spaces (trimmed)",
			raw:         "  test-456  ",
			expectError: false,
			expectedID:  "test-456",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			deliveryID, err := domain.NewDeliveryID(tt.raw)

			if tt.expectError {
				assert.Error(t, err)
				assert.True(t, deliveryID.IsEmpty())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedID, deliveryID.String())
				assert.False(t, deliveryID.IsEmpty())
			}
		})
	}
}

// ============================================================================
// WebhookEvent Tests - Unit Tests
// ============================================================================

func TestWebhookEvent_NewWebhookEvent_Unit(t *testing.T) {
	tests := []struct {
		name        string
		deliveryID  string
		source      string
		payload     []byte
		expectError bool
	}{
		{
			name:        "Valid webhook event",
			deliveryID:  "test-123",
			source:      "github.com",
			payload:     []byte(`{"event": "push"}`),
			expectError: false,
		},
		{
			name:        "Empty delivery ID",
			deliveryID:  "",
			source:      "github.com",
			payload:     []byte(`{"event": "push"}`),
			expectError: true,
		},
		{
			name:        "Empty source",
			deliveryID:  "test-123",
			source:      "",
			payload:     []byte(`{"event": "push"}`),
			expectError: true,
		},
		{
			name:        "Empty payload",
			deliveryID:  "test-123",
			source:      "github.com",
			payload:     []byte{},
			expectError: true,
		},
		{
			name:        "Whitespace source",
			deliveryID:  "test-123",
			source:      "   ",
			payload:     []byte(`{"event": "push"}`),
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			deliveryID, err := domain.NewDeliveryID(tt.deliveryID)
			if tt.deliveryID == "" {
				// Skip creating delivery ID for empty test case
				event, err := domain.NewWebhookEvent(domain.DeliveryID{}, tt.source, tt.payload)
				assert.Error(t, err)
				assert.Nil(t, event)
				return
			}
			require.NoError(t, err)

			event, err := domain.NewWebhookEvent(deliveryID, tt.source, tt.payload)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, event)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, event)
				assert.Equal(t, tt.deliveryID, event.DeliveryID.String())
				assert.Equal(t, tt.source, event.Source)
				assert.Equal(t, tt.payload, event.Payload)
				assert.False(t, event.ReceivedAt.IsZero())
			}
		})
	}
}

func TestWebhookEvent_Validate_Unit(t *testing.T) {
	deliveryID, err := domain.NewDeliveryID("test-123")
	require.NoError(t, err)

	tests := []struct {
		name    string
		event   *domain.WebhookEvent
		wantErr bool
	}{
		{
			name: "Valid event",
			event: &domain.WebhookEvent{
				DeliveryID: deliveryID,
				Source:     "github.com",
				Payload:    []byte(`{"event": "push"}`),
				ReceivedAt: domain.NewReceivedAt().Time,
			},
			wantErr: false,
		},
		{
			name: "Empty delivery ID",
			event: &domain.WebhookEvent{
				DeliveryID: domain.DeliveryID{},
				Source:     "github.com",
				Payload:    []byte(`{"event": "push"}`),
				ReceivedAt: domain.NewReceivedAt().Time,
			},
			wantErr: true,
		},
		{
			name: "Empty source",
			event: &domain.WebhookEvent{
				DeliveryID: deliveryID,
				Source:     "",
				Payload:    []byte(`{"event": "push"}`),
				ReceivedAt: domain.NewReceivedAt().Time,
			},
			wantErr: true,
		},
		{
			name: "Empty payload",
			event: &domain.WebhookEvent{
				DeliveryID: deliveryID,
				Source:     "github.com",
				Payload:    []byte{},
				ReceivedAt: domain.NewReceivedAt().Time,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.event.Validate()
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestWebhookEvent_GetKey_Unit(t *testing.T) {
	deliveryID, err := domain.NewDeliveryID("test-123")
	require.NoError(t, err)

	event, err := domain.NewWebhookEvent(deliveryID, "github.com", []byte(`{"event": "push"}`))
	require.NoError(t, err)

	key := event.GetKey()
	expected := "github.com:test-123"
	assert.Equal(t, expected, key)
}

func TestDomainErrors(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected string
	}{
		{"Invalid delivery ID", domain.ErrInvalidDeliveryID, "invalid delivery ID"},
		{"Delivery ID too long", domain.ErrDeliveryIDTooLong, "delivery ID too long"},
		{"Invalid source", domain.ErrInvalidSource, "invalid source"},
		{"Empty payload", domain.ErrEmptyPayload, "empty payload"},
		{"Invalid received at", domain.ErrInvalidReceivedAt, "invalid received at timestamp"},
		{"Invalid webhook event", domain.ErrInvalidWebhookEvent, "invalid webhook event"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Error(t, tt.err)
			assert.Equal(t, tt.expected, tt.err.Error())
		})
	}
}

func TestReceivedAt(t *testing.T) {
	receivedAt := domain.NewReceivedAt()
	assert.False(t, receivedAt.Time.IsZero())

	// Should be recent (within last second)
	now := receivedAt.Time
	assert.True(t, now.After(receivedAt.Time.Add(-1000000000))) // 1 second ago
}

func TestDeliveryID_String(t *testing.T) {
	deliveryID, err := domain.NewDeliveryID("test-123")
	require.NoError(t, err)

	assert.Equal(t, "test-123", deliveryID.String())
}

func TestDeliveryID_IsEmpty(t *testing.T) {
	// Empty delivery ID
	emptyID := domain.DeliveryID{}
	assert.True(t, emptyID.IsEmpty())

	// Non-empty delivery ID
	deliveryID, err := domain.NewDeliveryID("test-123")
	require.NoError(t, err)
	assert.False(t, deliveryID.IsEmpty())
}
