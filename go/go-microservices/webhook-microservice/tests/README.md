# 🧪 Webhook Microservice Test Suite

A comprehensive, beautifully formatted test suite for the webhook microservice following Domain-Driven Design and Clean Architecture principles.

## 🚀 Quick Start

```bash
# Run all tests with beautiful output
bash scripts/run-tests.sh

# Run only unit tests (fast)
bash scripts/run-tests.sh unit

# Run only integration tests (requires running service)
bash scripts/run-tests.sh integration

# Generate coverage report
bash scripts/run-tests.sh coverage

# Show help
bash scripts/run-tests.sh help
```

## 🎨 Beautiful Test Output

Our enhanced test runner provides:

- 🏃 **Running tests** - Cyan with running emoji
- ✅ **Passing tests** - Green with checkmark
- ❌ **Failing tests** - Red with cross mark
- ⏭️ **Skipped tests** - Yellow with skip emoji
- 📦 **Package results** - Organized by test suites
- ⏱️ **Timing information** - Duration for each test
- 🏆 **Beautiful summaries** - Clear pass/fail statistics

## 📁 Test Organization

### Unit Tests (`*_Unit`)
Fast, isolated tests with no external dependencies:

- **Domain Tests** - Business logic and domain models
- **Application Tests** - Command handlers and use cases
- **API Tests** - HTTP handlers and middleware
- **Error Tests** - Error handling and validation

### Integration Tests (`*_Integration`)
End-to-end tests with running service:

- **Webhook Processing** - Full webhook flow
- **Health Endpoints** - Service health checks
- **Dapr Integration** - External service communication

## 🏗️ Test Architecture

```
tests/
├── domain_test.go      # Domain layer tests (business logic)
├── app_test.go         # Application layer tests (use cases)
├── api_test.go         # API layer tests (HTTP handlers)
├── middleware_test.go  # Middleware tests (WAF, validation)
├── response_test.go    # API response tests
├── errors_test.go      # Error handling tests
├── config_test.go      # Configuration tests
├── infra_test.go       # Infrastructure tests
└── webhook_test.go     # Integration tests
```

## 🎯 Test Coverage Goals

- **Domain Layer**: 100% coverage of business logic
- **Application Layer**: All command handlers and use cases
- **API Layer**: All endpoints, middleware, and error scenarios
- **Infrastructure Layer**: Service creation and integration
- **Integration**: End-to-end webhook processing

## 🔧 Test Types

### 🏃‍♂️ Unit Tests
- **Speed**: Very fast (< 1 second)
- **Dependencies**: None (mocked)
- **Scope**: Individual functions/methods
- **When to run**: During development, CI/CD

### 🚀 Integration Tests
- **Speed**: Moderate (5-10 seconds)
- **Dependencies**: Running service + Dapr
- **Scope**: End-to-end workflows
- **When to run**: Before deployment, staging

### 📊 Coverage Tests
- **Speed**: Moderate (5-10 seconds)
- **Output**: HTML coverage report
- **Scope**: All code paths
- **When to run**: Quality assurance, code review

## 🛠️ Prerequisites

### For Unit Tests
- Go 1.21+
- No external dependencies

### For Integration Tests
- Running webhook service: `bash scripts/run-local.sh`
- Service on port 8080
- Dapr on port 3500 (optional but recommended)

## 📈 Coverage Reports

Generate detailed coverage reports:

```bash
# Generate coverage report
bash scripts/run-tests.sh coverage

# Open HTML report in browser
open coverage.html
```

## 🎨 Customization

### Test Output Formatting
The test formatter (`scripts/test-formatter.sh`) can be customized:

- Colors and symbols
- Output verbosity
- Summary format

### Test Categories
Add new test categories by following the naming convention:
- `TestName_Unit` for unit tests
- `TestName_Integration` for integration tests

## 🐛 Troubleshooting

### Integration Tests Failing
```bash
# Check if service is running
curl http://localhost:8080/healthz

# If not, start the service
bash scripts/run-local.sh
```

### Coverage Issues
```bash
# Clean and rebuild
go clean -testcache
go mod tidy
```

## 🏆 Best Practices

1. **Test Naming**: Use descriptive names with `_Unit` or `_Integration` suffix
2. **Test Organization**: Group related tests in the same file
3. **Assertions**: Use testify for clear, readable assertions
4. **Mocking**: Mock external dependencies in unit tests
5. **Coverage**: Aim for high coverage but focus on critical paths

## 🎉 Success Metrics

A successful test run shows:
- ✅ All unit tests passing
- ✅ All integration tests passing (if service is running)
- 🏆 Beautiful summary with pass/fail statistics
- 📊 High code coverage (>80%)

---

**Happy Testing!** 🧪✨
