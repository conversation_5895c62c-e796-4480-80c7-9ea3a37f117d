# Test Documentation

This directory contains all tests for the template microservice, organized by test type and scope.

## Test Structure

```
tests/
├── unit/                    # Unit tests (fast, isolated)
│   ├── container/          # Container initialization and configuration tests
│   │   ├── container_test.go           # Basic container functionality
│   │   ├── graceful_degradation_test.go # Resilience and graceful degradation
│   │   └── benchmark_test.go           # Performance benchmarks
│   ├── app/                # Application service layer tests
│   │   └── services_test.go            # TemplateService business logic
│   └── messaging/          # Event publishing tests
│       └── event_publisher_test.go     # Event publisher functionality
├── integration/            # Integration tests (cross-component)
│   └── template_test.go    # Template workflow integration tests
└── e2e/                    # End-to-end tests (full system)
    └── template_test.go    # Complete API workflow tests
```

## Test Types

### Unit Tests (`tests/unit/`)
- **Purpose**: Test individual components in isolation
- **Speed**: Fast (< 1s per test)
- **Dependencies**: Mocked/stubbed
- **Scope**: Single function/method/class

### Integration Tests (`tests/integration/`)
- **Purpose**: Test component interactions
- **Speed**: Medium (1-10s per test)
- **Dependencies**: Real databases, mocked external services
- **Scope**: Multiple components working together

### End-to-End Tests (`tests/e2e/`)
- **Purpose**: Test complete user workflows
- **Speed**: Slow (10s+ per test)
- **Dependencies**: Full system with real services
- **Scope**: Complete application functionality

## Running Tests

### All Tests
```bash
# Run all tests
go test ./tests/...

# Run all tests with verbose output
go test -v ./tests/...

# Run all tests with coverage
go test -cover ./tests/...
```

### By Test Type
```bash
# Unit tests only (fast)
go test ./tests/unit/...

# Integration tests only
go test ./tests/integration/...

# End-to-end tests only
go test ./tests/e2e/...
```

### Specific Components
```bash
# Container tests only
go test ./tests/unit/container/...

# App service tests only
go test ./tests/unit/app/...

# Messaging tests only
go test ./tests/unit/messaging/...
```

### Performance Tests
```bash
# Run benchmarks
go test -bench=. ./tests/unit/container/

# Run benchmarks with memory allocation stats
go test -bench=. -benchmem ./tests/unit/container/

# Run specific benchmark
go test -bench=BenchmarkContainerCreation ./tests/unit/container/
```

## Test Environment Setup

### Unit Tests
Unit tests are designed to run without external dependencies:
- Databases are mocked
- Dapr is disabled
- External services are stubbed

### Integration Tests
Integration tests require:
- ClickHouse database (optional, graceful degradation)
- MongoDB database (optional, graceful degradation)
- Redis for caching (optional)

### End-to-End Tests
E2E tests require the full environment:
- All databases running
- Dapr sidecar running
- All external services available

## Environment Variables for Testing

```bash
# Disable external dependencies for unit tests
export DAPR_ENABLED=false
export CLICKHOUSE_DSN=""
export MONGODB_URI=""
export TRACING_ENABLED=false

# Enable components for integration tests
export DAPR_ENABLED=true
export CLICKHOUSE_DSN="tcp://localhost:9000/templates"
export MONGODB_URI="mongodb://localhost:27017"
```

## Test Patterns

### Container Tests
- Test graceful degradation when dependencies are unavailable
- Validate environment-first configuration loading
- Ensure thread-safe initialization
- Benchmark performance to prevent regression

### Service Tests
- Use mocks for dependencies (repositories, event publishers)
- Test both success and error scenarios
- Validate business logic without external dependencies
- Test event publishing behavior

### Integration Tests
- Test real database interactions
- Validate cross-component communication
- Test error handling with real failures
- Validate configuration loading

## Continuous Integration

### Test Pipeline
1. **Unit Tests**: Run on every commit (fast feedback)
2. **Integration Tests**: Run on pull requests
3. **E2E Tests**: Run on main branch and releases
4. **Performance Tests**: Run nightly or on performance-critical changes

### Coverage Requirements
- **Unit Tests**: > 80% coverage
- **Integration Tests**: Critical paths covered
- **E2E Tests**: Main user workflows covered

## Writing New Tests

### Unit Test Guidelines
1. Use external test packages (`package component_test`)
2. Mock all external dependencies
3. Test both success and error paths
4. Keep tests fast and isolated
5. Use descriptive test names

### Integration Test Guidelines
1. Test real component interactions
2. Use test databases/services when possible
3. Clean up resources after tests
4. Test configuration variations
5. Validate error handling

### E2E Test Guidelines
1. Test complete user workflows
2. Use realistic test data
3. Validate API responses
4. Test error scenarios
5. Keep tests independent

## Test Utilities

### Mocking
- Use `testify/mock` for interface mocking
- Create reusable mock implementations
- Validate mock expectations

### Test Data
- Use factory functions for test data creation
- Keep test data minimal but realistic
- Use unique identifiers to avoid conflicts

### Assertions
- Use `testify/assert` and `testify/require`
- Prefer `require` for critical assertions
- Use descriptive assertion messages

## Troubleshooting

### Common Issues
1. **Tests fail with database connection errors**: Check if databases are running or disable them for unit tests
2. **Dapr-related test failures**: Ensure Dapr sidecar is running or disable Dapr for unit tests
3. **Flaky tests**: Check for race conditions or shared state between tests
4. **Slow tests**: Move expensive operations to integration/e2e tests

### Debug Tips
1. Use `go test -v` for verbose output
2. Add temporary log statements for debugging
3. Run individual tests with `go test -run TestName`
4. Use `go test -race` to detect race conditions 