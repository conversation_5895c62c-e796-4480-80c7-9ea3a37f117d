package integration

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"template_microservice/internal/domain"
)

const (
	baseURL = "http://localhost:8080"
)

func TestTemplateCRUD(t *testing.T) {
	// Wait for service to be ready
	require.Eventually(t, func() bool {
		resp, err := http.Get(baseURL + "/readyz")
		return err == nil && resp.StatusCode == http.StatusOK
	}, 30*time.Second, time.Second)

	// Test data
	template := domain.Template{
		Name:    "Test Template",
		Content: "Test Content",
	}

	// Create template
	createResp, err := createTemplate(template)
	require.NoError(t, err)
	assert.NotEmpty(t, createResp.ID)
	assert.Equal(t, template.Name, createResp.Name)
	assert.Equal(t, template.Content, createResp.Content)

	// Get template
	getResp, err := getTemplate(createResp.ID)
	require.NoError(t, err)
	assert.Equal(t, createResp.ID, getResp.ID)
	assert.Equal(t, template.Name, getResp.Name)
	assert.Equal(t, template.Content, getResp.Content)
}

func createTemplate(template domain.Template) (*domain.Template, error) {
	body, err := json.Marshal(template)
	if err != nil {
		return nil, err
	}

	resp, err := http.Post(baseURL+"/api/v1/templates", "application/json", bytes.NewBuffer(body))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	var result domain.Template
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, err
	}

	return &result, nil
}

func getTemplate(id string) (*domain.Template, error) {
	resp, err := http.Get(baseURL + "/api/v1/templates/" + id)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	var result domain.Template
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, err
	}

	return &result, nil
} 