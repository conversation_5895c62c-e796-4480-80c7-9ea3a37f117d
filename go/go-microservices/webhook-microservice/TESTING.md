# Webhook Microservice Testing Guide

This guide provides comprehensive testing procedures to verify all components work according to the DDD specification.

## 🚀 Quick Automated Testing

Run the automated test suite:

```bash
./scripts/test-microservice.sh
```

This tests all major functionality automatically.

## 🔍 Manual Testing Procedures

### 1. Infrastructure Health Checks

```bash
# Webhook service health
curl http://localhost:8080/healthz
# Expected: {"status":"ok"}

# Webhook service readiness  
curl http://localhost:8080/readyz
# Expected: {"status":"ready"}

# Dapr health
curl -I http://localhost:3500/v1.0/healthz
# Expected: HTTP/1.1 204 No Content

# Metrics endpoint
curl http://localhost:8080/metrics
# Expected: Prometheus metrics format
```

### 2. Dapr Component Verification

```bash
# Check loaded components
curl http://localhost:3500/v1.0/metadata | jq '.components'
# Expected: statestore and redis-pubsub components

# Verify state store
curl http://localhost:3500/v1.0/metadata | jq '.components[] | select(.name=="statestore")'

# Verify pub/sub
curl http://localhost:3500/v1.0/metadata | jq '.components[] | select(.name=="redis-pubsub")'
```

### 3. WAF Middleware Testing

```bash
# Test 1: No headers (should be blocked)
curl -X POST http://localhost:8080/webhook -d '{"test":"data"}'
# Expected: 403 Forbidden

# Test 2: Missing delivery ID
curl -X POST http://localhost:8080/webhook \
  -H 'Content-Type: application/json' \
  -H 'X-Source: test' \
  -d '{"test":"data"}'
# Expected: 400 Bad Request

# Test 3: Missing source
curl -X POST http://localhost:8080/webhook \
  -H 'Content-Type: application/json' \
  -H 'X-Delivery-ID: test-123' \
  -d '{"test":"data"}'
# Expected: 400 Bad Request
```

### 4. Valid Webhook Processing

```bash
# Test valid webhook
curl -X POST http://localhost:8080/webhook \
  -H 'Content-Type: application/json' \
  -H 'X-Delivery-ID: test-123' \
  -H 'X-Source: github.com' \
  -d '{"event":"push","repository":"test-repo"}'
# Expected: {"status":"processed","delivery_id":"test-123","source":"github.com"}
```

### 5. Deduplication Testing

```bash
# Send the same webhook twice
DELIVERY_ID="duplicate-test-$(date +%s)"

# First request
curl -X POST http://localhost:8080/webhook \
  -H 'Content-Type: application/json' \
  -H "X-Delivery-ID: $DELIVERY_ID" \
  -H 'X-Source: github.com' \
  -d '{"event":"push"}'
# Expected: {"status":"processed",...}

# Second request (duplicate)
curl -X POST http://localhost:8080/webhook \
  -H 'Content-Type: application/json' \
  -H "X-Delivery-ID: $DELIVERY_ID" \
  -H 'X-Source: github.com' \
  -d '{"event":"push"}'
# Expected: {"status":"already_processed",...}
```

### 6. State Store Verification

```bash
# Check if webhook was stored in Dapr state
curl "http://localhost:3500/v1.0/state/statestore/webhook:processed:$DELIVERY_ID"
# Expected: Timestamp when webhook was processed
```

### 7. Error Handling

```bash
# Invalid JSON
curl -X POST http://localhost:8080/webhook \
  -H 'Content-Type: application/json' \
  -H 'X-Delivery-ID: invalid-json' \
  -H 'X-Source: test' \
  -d 'invalid-json{'
# Expected: 400 Bad Request

# Empty payload
curl -X POST http://localhost:8080/webhook \
  -H 'Content-Type: application/json' \
  -H 'X-Delivery-ID: empty-test' \
  -H 'X-Source: test' \
  -d ''
# Expected: 400 Bad Request

# Invalid delivery ID (too long)
curl -X POST http://localhost:8080/webhook \
  -H 'Content-Type: application/json' \
  -H 'X-Delivery-ID: '$(printf 'a%.0s' {1..300}) \
  -H 'X-Source: test' \
  -d '{"test":"data"}'
# Expected: 400 Bad Request
```

### 8. Observability Testing

#### Metrics Verification
```bash
# Check for webhook-specific metrics
curl http://localhost:8080/metrics | grep http_requests_total
curl http://localhost:8080/metrics | grep http_request_duration

# Check Dapr metrics
curl http://localhost:9090/metrics | grep dapr
```

#### Tracing Verification
1. Open Jaeger UI: http://localhost:16686
2. Select service: `webhook-microservice`
3. Click "Find Traces"
4. Look for traces with operation: `webhook.handle`
5. Verify trace contains:
   - Span attributes (delivery_id, result)
   - Proper error recording for failed requests
   - Duration measurements

#### Logging Verification
Check the application logs for structured logging:
```bash
# Look for structured JSON logs
docker logs webhook-microservice-container | jq .
```

### 9. Performance Testing

#### Rate Limiting
```bash
# Send multiple requests quickly
for i in {1..20}; do
  curl -X POST http://localhost:8080/webhook \
    -H 'Content-Type: application/json' \
    -H "X-Delivery-ID: rate-test-$i" \
    -H 'X-Source: test' \
    -d '{"test":"data"}' &
done
wait
# Expected: Some requests should return 429 Too Many Requests
```

#### Load Testing (Optional)
```bash
# Using Apache Bench (if installed)
ab -n 100 -c 10 -H "Content-Type: application/json" \
   -H "X-Delivery-ID: load-test" \
   -H "X-Source: test" \
   -p <(echo '{"test":"data"}') \
   http://localhost:8080/webhook
```

## 🎯 DDD Specification Compliance Checklist

- [ ] **Domain Layer**: Webhook events, delivery IDs, validation rules
- [ ] **Application Layer**: Command handlers, error mapping, HTTP status codes
- [ ] **Infrastructure Layer**: Dapr state/pubsub, tracing, metrics
- [ ] **API Layer**: HTTP handlers, middleware (WAF, validator), error responses
- [ ] **Configuration**: Environment-based config, webhook-specific settings
- [ ] **Observability**: Structured logging, metrics, tracing
- [ ] **Security**: WAF with IP filtering, rate limiting, input validation
- [ ] **Reliability**: Deduplication, error handling, graceful degradation

## 🐛 Troubleshooting

### Common Issues

1. **403 Forbidden**: Check WAF configuration and required headers
2. **Dapr Connection Failed**: Ensure Dapr sidecar is running
3. **State Not Found**: Check Redis connection and Dapr state store config
4. **Metrics Missing**: Verify Prometheus middleware is enabled
5. **Traces Missing**: Check Jaeger configuration and tracing setup

### Debug Commands

```bash
# Check Dapr components
dapr components -k

# Check Dapr logs
docker logs dapr_*

# Check application logs
docker logs webhook-microservice-container

# Check Redis connection
docker exec dapr_redis redis-cli ping
```

## 📊 Expected Results

A fully working webhook microservice should:
- ✅ Accept valid webhooks and return 200
- ✅ Reject invalid requests with appropriate error codes
- ✅ Deduplicate webhooks based on delivery ID
- ✅ Store state in Dapr/Redis
- ✅ Publish events via Dapr pub/sub
- ✅ Generate metrics and traces
- ✅ Apply rate limiting and WAF protection
- ✅ Handle errors gracefully with proper logging
