# Webhook Microservice Environment Variables

# Server Configuration
SERVER_PORT=8080
SERVER_HOST=0.0.0.0

# Environment
ENVIRONMENT=development

# Logging
LOG_LEVEL=info

# Dapr Configuration
DAPR_APP_ID=webhook-microservice
DAPR_HTTP_PORT=3500
DAPR_GRPC_PORT=50001
DAPR_STATE_STORE=statestore
DAPR_PUBSUB_NAME=redis-pubsub

# Webhook Configuration
WEBHOOK_TOPIC_NAME=webhook-events
WEBHOOK_RATE_LIMIT_RPM=1000

# Redis Configuration
REDIS_ADDR=localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# Observability
JAEGER_ENDPOINT=http://localhost:14268/api/traces
PROMETHEUS_ENABLED=true

# WAF Configuration (comma-separated)
WAF_ALLOW_LIST=0.0.0.0/0
