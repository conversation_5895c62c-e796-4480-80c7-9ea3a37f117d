server:
  port: 8080
  host: "0.0.0.0"

environment: development

logging:
  level: info  # debug, info, warn, error

observability:
  prometheus:
    enabled: true
    path: "/metrics"
  tracing:
    enabled: false
    jaeger: "http://localhost:14268/api/traces"

clickhouse:
  dsn: "clickhouse://localhost:9000/templates?username=default&password="

mongodb:
  uri: "mongodb://localhost:27017"

redis:
  addr: "localhost:6380"
  password: ""
  db: 0
  pool_size: 10
  min_idle_conns: 5
  max_retries: 3
  key_prefix: "template:"
  default_ttl: 3600  # 1 hour in seconds

# Dapr configuration
dapr:
  enabled: true
  app_id: "template-microservice"
  http_port: 3500
  grpc_port: 50001
  state_store: "statestore"
  pubsub_name: "redis-pubsub"
  secret_store: "config-secret-store"

# Secrets that can be accessed via Dapr or directly
secrets:
  database:
    connection_string: "mongodb://localhost:27017"
    username: "admin"
    password: "secret123"
  api:
    key: "your-api-key-here"
    secret: "your-api-secret-here"
  external:
    service_a_key: "service-a-api-key"
    service_b_secret: "service-b-secret-token" 