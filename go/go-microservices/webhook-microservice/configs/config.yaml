server:
  port: 8080
  host: 0.0.0.0

environment: development

logging:
  level: info  # debug, info, warn, error

observability:
  prometheus:
    enabled: true
    path: /metrics
  tracing:
    enabled: false
    jaeger: http://localhost:14268/api/traces

# Dapr configuration for webhook microservice
dapr:
  enabled: true
  app_id: webhook-microservice
  http_port: 3500
  grpc_port: 50001
  state_store: statestore
  pubsub_name: redis-pubsub
  secret_store: config-secret-store

# Webhook-specific configuration
webhook:
  topic_name: webhook-events
  rate_limit_rpm: 1000  # requests per minute
  waf_allow_list:
    - "0.0.0.0/0"  # Allow all IPs in development
  required_headers:
    - "X-Delivery-ID"  # Generic delivery ID (will try alternatives automatically)

  # Supported webhook sources and their headers
  sources:
    github:
      delivery_headers: ["X-GitHub-Delivery", "X-GitHub-Hook-ID"]
      event_header: "X-GitHub-Event"
      signature_headers: ["X-Hub-Signature-256", "X-Hub-Signature"]
      user_agent_pattern: "GitHub-Hookshot"
    jira:
      delivery_headers: ["X-Atlassian-Webhook-Identifier"]
      event_header: "X-Event-Key"
      signature_headers: []
      user_agent_pattern: "Atlassian"
    crosschex:
      delivery_headers: ["X-CrossChex-Delivery-ID", "X-Delivery-ID"]
      event_header: "X-CrossChex-Event"
      signature_headers: []
      user_agent_pattern: "CrossChex"

# Redis configuration for state store and pub/sub
redis:
  addr: "localhost:6379"
  password: ""
  db: 0
  pool_size: 10
  min_idle_conns: 5
  max_retries: 3
  key_prefix: "webhook:"
  default_ttl: 86400  # 24 hours in seconds for deduplication