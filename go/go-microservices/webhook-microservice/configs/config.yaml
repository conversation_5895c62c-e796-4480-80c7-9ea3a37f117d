server:
  port: 8080
  host: localhost

environment: development

logging:
  level: info  # debug, info, warn, error

observability:
  prometheus:
    enabled: true
    path: /metrics
  tracing:
    enabled: false
    jaeger: http://localhost:14268/api/traces

clickhouse:
  dsn: "tcp://localhost:9000/templates?username=default&password="

mongodb:
  uri: "mongodb://localhost:27017"

redis:
  addr: "localhost:6380"
  password: ""
  db: 0
  pool_size: 10
  min_idle_conns: 5
  max_retries: 3
  key_prefix: "template:"
  default_ttl: 3600  # 1 hour in seconds 