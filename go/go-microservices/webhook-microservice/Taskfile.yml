version: '3'

tasks:
  dev:
    desc: Run the service in development mode
    cmds:
      - bash scripts/run-local.sh

  fmt:
    desc: Format all Go files
    cmds:
      - go fmt ./...

  lint:
    desc: Run linters
    cmds:
      - go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
      - golangci-lint run

  test:
    desc: Run all tests
    cmds:
      - go test -v ./...

  test:integration:
    desc: Run integration tests
    cmds:
      - go test -v ./tests/integration/...

  test:e2e:
    desc: Run end-to-end tests
    cmds:
      - go test -v ./tests/e2e/...

  test:coverage:
    desc: Run tests with coverage
    cmds:
      - go test -coverprofile=coverage.out ./...
      - go tool cover -html=coverage.out

  clean:
    desc: Clean build artifacts
    cmds:
      - rm -rf coverage.out
      - go clean 