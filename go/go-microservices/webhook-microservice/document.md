# Go/Gin Microservice Boilerplate Specification

This document defines the requirements and structure for a runnable Go/Gin microservice boilerplate, pre-wired with ClickHouse, MongoDB, Gin and Dapr integrations.

---

## 1. Framework & Runtime

**Gin** for HTTP routing  
Service boots with:  
  go run ./cmd/modelmonitoring

---

## 2. Persistence Integrations

**ClickHouse**  
  - Real repository implementation (DSN from config) using the official ClickHouse driver  
  - migrations/ folder with example SQL migration and automated runner in run-local.sh  
  - Healthcheck and connection pooling configured  
**MongoDB**  
  - Real repository implementation (URI from config) using the official MongoDB driver  
  - Example collection initialization (indexes created on startup)  

---

## 3. Dapr

**State Management** client stub  
**Pub/Sub** subscription handler scaffold  
**Service Invocation** client stub  
**Secrets Management** client stub  

---

## 4. Architecture

**DDD**  
  - internal/domain for entities, value-objects, domain events  
**CQRS**  
  - internal/app for command/query handlers  
  - ports.go for persistence and messaging interfaces  
**Container Pattern**  
  - internal/container/container.go wires together config, logger, tracer, metrics, Dapr clients, real DB repositories, and application services  

---

## 5. Observability

**Prometheus** middleware for HTTP metrics  
**Zap** for request and error logging (with request IDs)  
**OpenTelemetry** instrumentation with Jaeger exporter  

---

## 6. Sample “Template” Feature

A minimal aggregate demonstrating the full flow with real storage:

**Endpoints**  
  - POST /api/v1/templates → CreateTemplate (persists in ClickHouse & MongoDB)  
  - GET  /api/v1/templates/:id → GetTemplate  

---

## 7. Utilities & Shared

config package to load and validate .env / YAML / JSON  
Standard error envelope and HTTP response helpers  
Common middleware: CORS, recovery, rate-limit stub  

---

## 8. Tests & Dev Tasks

**Integration tests** (tests/integration/) using Docker-Compose for ClickHouse, MongoDB, and Dapr  
**End-to-end tests** (tests/e2e/) hitting /healthz, /readyz, and template endpoints  
**Dev tasks** in Taskfile.yml (or Makefile): dev, fmt, lint, test  
**Local bootstrap script** (run-local.sh):  
  1. Export required environment variables  
  2. Start Dapr sidecar and databases (docker-compose up -d)  
  3. Run migrations  
  4. Launch the service  

---

## 9. Directory Structure

```
template-microservice/  
├── cmd/  
│   └── server/  
│       └── main.go               # Bootstrap container, Gin router, start server  
├── configs/  
│   └── config.example.yaml       # \`.env\`/YAML/JSON templates  
├── scripts/  
│   └── run-local.sh              # Bootstrap Dapr, databases, migrations & run service  
├── docker-compose.yml            # ClickHouse, MongoDB, Dapr sidecar for local dev  
├── internal/  
│   ├── config/  
│   │   └── config.go             # Load & validate env/YAML into typed structs  
│   ├── container/  
│   │   └── container.go          # Wire config, logger, tracer, metrics, Dapr, real DB repos, services  
│   ├── api/  
│   │   ├── router.go             # Gin routes & middleware (CORS, recovery, request IDs)  
│   │   ├── health.go             # \`/healthz\`, \`/readyz\` handlers  
│   │   └── template_handler.go   # HTTP ↔ commands/queries for “Template” feature  
│   ├── app/  
│   │   ├── commands.go           # CreateTemplateCommand  
│   │   ├── queries.go            # GetTemplateQuery  
│   │   ├── ports.go              # TemplateRepository interface  
│   │   └── errors.go             # Application-level errors → HTTP mappings  
│   ├── domain/  
│   │   └── template.go           # \`Template\` aggregate (ID, Name, etc.)  
│   └── infra/  
│       ├── clickhouse/  
│       │   └── template_repo.go  # ClickHouse implementation (real SQL queries)  
│       ├── mongodb/  
│       │   └── template_repo.go  # MongoDB implementation (real driver calls)  
│       └── dapr/  
│           ├── state_client.go   # State management stub  
│           ├── pubsub.go         # Pub/Sub subscription scaffold  
│           ├── invocation.go     # Service invocation stub  
│           └── secrets.go        # Secrets management stub  
├── migrations/  
│   └── 0001_create_tables.sql    # Example ClickHouse/SQL migration  
├── tests/  
│   ├── integration/              # Docker-Compose spin-up + real DB & Dapr tests  
│   └── e2e/                      # Full-stack scenarios (health & template endpoints)  
├── Taskfile.yml                  # \`dev\`, \`fmt\`, \`lint\`, \`test\` tasks  
├── .env.example  
├── .gitignore  
├── go.mod  
├── go.sum  
├── README.md                     # Overview & directory reference  
└── run-local.sh                  # Alias for \`scripts/run-local.sh\` (executable)
```