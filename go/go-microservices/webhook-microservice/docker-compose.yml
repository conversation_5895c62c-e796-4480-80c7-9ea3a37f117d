services:
  clickhouse:
    image: clickhouse/clickhouse-server:23.8
    container_name: clickhouse
    ports:
      - "8123:8123"   # HTTP interface
      - "9000:9000"   # Native client
    volumes:
      - clickhouse_data:/var/lib/clickhouse
      - ./migrations:/docker-entrypoint-initdb.d
    environment:
      - CLICKHOUSE_DB=templates
      - CLICKHOUSE_USER=default
      - CLICKHOUSE_PASSWORD=
      - CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT=1

  mongodb:
    image: mongo:6.0
    container_name: mongodb
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      - MONGO_INITDB_DATABASE=templates

  redis:
    image: redis:7.2
    container_name: redis
    ports:
      - "6380:6379"  # Changed from 6379:6379 to avoid port conflict
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

volumes:
  clickhouse_data:
  mongodb_data:
  redis_data: 